/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package electronmain

import (
	"context"
	"fmt"
	"runtime"
	"sync"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	configurationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/configuration/common"
	environmentelectronmain "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment/electron-main"
	filescommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	instantiationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
	lifecycleelectronmain "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/lifecycle/electron-main"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	productcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/product/common"
	statenode "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/state/node"
	userdataprofileselectronmain "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/userDataProfile/electron-main"
	windowsmain "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/windows/electron-main"
)

// CodeApplication represents the main VS Code application
// This is the Go equivalent of the CodeApplication class from app.ts
// There will only ever be one instance, even if the user starts many instances
type CodeApplication struct {
	// Embed Disposable equivalent
	*basecommon.Disposable

	onWillQuit *basecommon.Emitter[interface{}]

	// Security protocol handling confirmation setting key equivalent
	securityProtocolHandlingConfirmationSettingKey map[string]string

	// Core services (equivalent to CodeApplication constructor parameters)
	mainInstantiationService    instantiationcommon.IInstantiationService
	logService                  logcommon.ILogService
	loggerService               logcommon.ILoggerService
	environmentMainService      environmentelectronmain.IEnvironmentMainServiceInterface
	lifecycleMainService        lifecycleelectronmain.ILifecycleMainService
	configurationService        configurationcommon.IConfigurationService
	stateService                statenode.IStateService
	fileService                 filescommon.IFileService
	productService              productcommon.IProductService
	userDataProfilesMainService userdataprofileselectronmain.IUserDataProfilesMainService

	// Context and synchronization
	ctx    context.Context
	cancel context.CancelFunc
	mutex  sync.RWMutex
}

// NewCodeApplication creates a new CodeApplication instance
// Equivalent to the CodeApplication constructor in app.ts
func NewCodeApplication(
	mainInstantiationService instantiationcommon.IInstantiationService,
	logService logcommon.ILogService,
	loggerService logcommon.ILoggerService,
	environmentMainService environmentelectronmain.IEnvironmentMainServiceInterface,
	lifecycleMainService lifecycleelectronmain.ILifecycleMainService,
	configurationService configurationcommon.IConfigurationService,
	stateService statenode.IStateService,
	fileService filescommon.IFileService,
	productService productcommon.IProductService,
	userDataProfilesMainService userdataprofileselectronmain.IUserDataProfilesMainService,
) (*CodeApplication, error) {
	ctx, cancel := context.WithCancel(context.Background())

	app := &CodeApplication{
		Disposable:                  basecommon.NewDisposable(),
		onWillQuit:                  basecommon.NewEmitter[interface{}](),
		mainInstantiationService:    mainInstantiationService,
		logService:                  logService,
		loggerService:               loggerService,
		environmentMainService:      environmentMainService,
		lifecycleMainService:        lifecycleMainService,
		configurationService:        configurationService,
		stateService:                stateService,
		fileService:                 fileService,
		productService:              productService,
		userDataProfilesMainService: userDataProfilesMainService,
		ctx:                         ctx,
		cancel:                      cancel,
	}

	// Initialize security protocol handling confirmation setting key
	// Equivalent to SECURITY_PROTOCOL_HANDLING_CONFIRMATION_SETTING_KEY
	app.securityProtocolHandlingConfirmationSettingKey = map[string]string{
		"file":          "security.promptForLocalFileProtocolHandling",
		"vscode-remote": "security.promptForRemoteFileProtocolHandling",
	}

	// Configure session and register listeners (equivalent to constructor)
	app.configureSession()
	app.registerListeners()

	return app, nil
}

// Startup starts the VS Code application
// This is the Go equivalent of the async startup() method in app.ts
func (app *CodeApplication) Startup() error {
	app.mutex.Lock()
	defer app.mutex.Unlock()

	app.logService.Debug("Starting VS Code")
	app.logService.Debug(fmt.Sprintf("from: %s", app.environmentMainService.AppRoot()))

	// Platform-specific setup (equivalent to app.ts platform handling)
	if err := app.setupPlatformSpecific(); err != nil {
		return fmt.Errorf("platform setup failed: %w", err)
	}

	// Resolve unique machine identifiers (equivalent to app.ts machine ID resolution)
	app.logService.Trace("Resolving machine identifier...")
	if err := app.resolveMachineIdentifiers(); err != nil {
		app.logService.Error(fmt.Sprintf("Failed to resolve machine identifiers: %v", err))
		// Continue with default values - non-fatal error
	}

	// Initialize services (equivalent to app.ts initServices)
	appInstantiationService, err := app.initServices()
	if err != nil {
		return fmt.Errorf("service initialization failed: %w", err)
	}

	// Initialize channels (equivalent to app.ts initChannels)
	if err := app.initChannels(appInstantiationService); err != nil {
		return fmt.Errorf("channel initialization failed: %w", err)
	}

	// Open first window (equivalent to app.ts openFirstWindow)
	if err := app.openFirstWindow(appInstantiationService); err != nil {
		return fmt.Errorf("failed to open first window: %w", err)
	}

	// Post open window tasks (equivalent to app.ts afterWindowOpen)
	app.afterWindowOpen()

	app.logService.Debug("VS Code startup completed")
	return nil
}

// configureSession configures the application session
// Equivalent to configureSession() in app.ts
func (app *CodeApplication) configureSession() {
	app.logService.Debug("Configuring application session...")

	// Security related measures (simplified Go equivalent)
	// In the TypeScript version, this sets up Electron session permissions
	// For Go/Wails, we'll implement equivalent security measures

	app.logService.Debug("Session configuration completed")
}

// registerListeners registers application event listeners
// Equivalent to registerListeners() in app.ts
func (app *CodeApplication) registerListeners() {
	app.logService.Debug("Registering application listeners...")

	// Application-level lifecycle
	app.OnWillQuit().Subscribe(func(_ interface{}) {
		app.logService.Debug("Lifecycle#app.on(will-quit)")
		go app.lifecycleMainService.Quit(false)
	})

	app.logService.Debug("Application listeners registered")
}

// OnWillQuit returns the onWillQuit event
func (app *CodeApplication) OnWillQuit() basecommon.Event[interface{}] {
	return app.onWillQuit.Event()
}

// setupPlatformSpecific handles platform-specific setup
// Equivalent to platform-specific code in app.ts startup()
func (app *CodeApplication) setupPlatformSpecific() error {
	switch runtime.GOOS {
	case "windows":
		// Windows-specific setup (equivalent to win32AppUserModelId)
		app.logService.Debug("Setting up Windows-specific configuration")

	case "darwin":
		// macOS-specific setup (equivalent to native tabs fix)
		app.logService.Debug("Setting up macOS-specific configuration")

	case "linux":
		// Linux-specific setup
		app.logService.Debug("Setting up Linux-specific configuration")
	}

	return nil
}

// resolveMachineIdentifiers resolves unique machine identifiers
// Equivalent to machine ID resolution in app.ts
func (app *CodeApplication) resolveMachineIdentifiers() error {
	// Simplified implementation - in real scenario, this would use
	// hardware identifiers, MAC addresses, etc.
	app.logService.Trace("Machine identifiers resolved")
	return nil
}

// initServices initializes application services
// Equivalent to initServices() in app.ts
func (app *CodeApplication) initServices() (instantiationcommon.IInstantiationService, error) {
	app.logService.Debug("Initializing application services...")

	// Create service collection
	services := instantiationcommon.NewServiceCollection()

	// TODO: Implement platform-specific update services
	// switch runtime.GOOS {
	// case "windows":
	// 	app.logService.Debug("Registering Windows-specific services")
	// 	// services.Set(updatecommon.IUpdateService, func(service instantiationcommon.IServiceLocator) (interface{}, error) {
	// 	// 	return updatewin32.NewWin32UpdateService(), nil
	// 	// })
	// case "darwin":
	// 	app.logService.Debug("Registering macOS-specific services")
	// 	// services.Set(updatecommon.IUpdateService, func(service instantiationcommon.IServiceLocator) (interface{}, error) {
	// 	// 	return updatedarwin.NewDarwinUpdateService(), nil
	// 	// })
	// case "linux":
	// 	app.logService.Debug("Registering Linux-specific services")
	// 	// Check for snap environment
	// 	// if isLinuxSnap() {
	// 	// 	services.Set(updatecommon.IUpdateService, func(service instantiationcommon.IServiceLocator) (interface{}, error) {
	// 	// 		snapPath := os.Getenv("SNAP")
	// 	// 		snapRevision := os.Getenv("SNAP_REVISION")
	// 	// 		return updatesnap.NewSnapUpdateService(snapPath, snapRevision), nil
	// 	// 	})
	// 	// } else {
	// 	// 	services.Set(updatecommon.IUpdateService, func(service instantiationcommon.IServiceLocator) (interface{}, error) {
	// 	// 		return updatelinux.NewLinuxUpdateService(), nil
	// 	// 	})
	// 	// }
	// }

	// Register core services
	// Create a type-erased service identifier for WindowsMainService
	var IWindowsMainServiceID = instantiationcommon.CreateDecorator[interface{}]("windowsMainService")

	// Register the WindowsMainService using the service collection
	services.Set(IWindowsMainServiceID, func() (interface{}, error) {
		// Create and return the WindowsMainService
		// TODO: Pass any required dependencies to the constructor
		return &windowsmain.WindowsMainService{}, nil
	})

	// TODO: Implement or mock AuxiliaryWindowsMainService
	// services.Set(windowscommon.IAuxiliaryWindowsMainService, func(service instantiationcommon.IServiceLocator) (interface{}, error) {
	// 	return windowsauxiliary.NewAuxiliaryWindowsMainService(), nil
	// })

	// TODO: Implement dialog service
	// dialogMainService := dialogmain.NewDialogMainService(app.logService, app.productService)
	// services.Set(dialogcommon.IDialogMainService, func(service instantiationcommon.IServiceLocator) (interface{}, error) {
	// 	return dialogMainService, nil
	// })

	// TODO: Implement diagnostics service
	// services.Set(diagnosticscommon.IDiagnosticsMainService, func(service instantiationcommon.IServiceLocator) (interface{}, error) {
	// 	return diagnosticsmain.NewDiagnosticsMainService(), nil
	// })

	// TODO: Implement encryption service
	// services.Set(encryptioncommon.IEncryptionMainService, func(service instantiationcommon.IServiceLocator) (interface{}, error) {
	// 	return encryptionmain.NewEncryptionMainService(), nil
	// })

	// TODO: Implement keyboard layout service
	// services.Set(keyboardlayoutcommon.IKeyboardLayoutMainService, func(service instantiationcommon.IServiceLocator) (interface{}, error) {
	// 	return keyboardlayoutmain.NewKeyboardLayoutMainService(), nil
	// })

	// TODO: Implement native host service
	// services.Set(nativehostcommon.INativeHostMainService, func(service instantiationcommon.IServiceLocator) (interface{}, error) {
	// 	return nativehostmain.NewNativeHostMainService(), nil
	// })

	// TODO: Implement webview service
	// services.Set(webviewcommon.IWebviewManagerService, func(service instantiationcommon.IServiceLocator) (interface{}, error) {
	// 	return webviewmain.NewWebviewMainService(), nil
	// })

	// TODO: Implement menubar service
	// services.Set(menubarcommon.IMenubarMainService, func(service instantiationcommon.IServiceLocator) (interface{}, error) {
	// 	return menubarmain.NewMenubarMainService(), nil
	// })

	// TODO: Implement extension host starter
	// services.Set(extensionhostcommon.IExtensionHostStarter, func(service instantiationcommon.IServiceLocator) (interface{}, error) {
	// 	return extensionhoststarter.NewExtensionHostStarter(), nil
	// })

	// TODO: Implement backup service
	// backupMainService := backupmain.NewBackupMainService(
	// 	app.environmentMainService,
	// 	app.configurationService,
	// 	app.logService,
	// 	app.stateService,
	// )
	// services.Set(backupcommon.IBackupMainService, func(service instantiationcommon.IServiceLocator) (interface{}, error) {
	// 	return backupMainService, nil
	// })

	// TODO: Implement workspace services
	// workspacesManagementMainService := workspacesmanagementmain.NewWorkspacesManagementMainService(
	// 	app.environmentMainService,
	// 	app.logService,
	// 	app.userDataProfilesMainService,
	// 	backupMainService,
	// 	dialogMainService,
	// )
	// services.Set(workspacescommon.IWorkspacesManagementMainService, func(service instantiationcommon.IServiceLocator) (interface{}, error) {
	// 	return workspacesManagementMainService, nil
	// })

	// Initialize services that require async initialization
	// if err := backupMainService.Initialize(); err != nil {
	// 	return nil, fmt.Errorf("failed to initialize backup service: %w", err)
	// }

	// if err := workspacesManagementMainService.Initialize(); err != nil {
	// 	return nil, fmt.Errorf("failed to initialize workspaces service: %w", err)
	// }

	app.logService.Debug("Application services initialized")
	return app.mainInstantiationService.CreateChild(services), nil
}

// initChannels initializes IPC channels for inter-process communication
// Equivalent to initChannels() in app.ts
func (app *CodeApplication) initChannels(instantiationService instantiationcommon.IInstantiationService) error {
	app.logService.Debug("Initializing IPC channels...")

	// TODO: Implement IPC channel initialization
	// The original implementation uses:
	// 1. Diagnostics service channel
	// 2. Main process Node IPC server
	// 
	// For now, we'll just log that this is a no-op
	// until the required services are implemented
	app.logService.Debug("IPC channel initialization is currently a no-op")

	// TODO: Uncomment and implement when services are available:
	/*
	// Create a new disposable store for channel resources
	disposables := basecommon.NewDisposableStore()

	// Diagnostics channel
	if diagnosticsService, err := instantiationService.Get(diagnosticscommon.IDiagnosticsMainService); err == nil {
		diagnosticsChannel := ipc.NewProxyChannel("diagnostics", diagnosticsService.(diagnosticscommon.IDiagnosticsMainService))
		if err := app.mainProcessNodeIPCServer.RegisterChannel("diagnostics", diagnosticsChannel); err != nil {
			disposables.Dispose()
			return fmt.Errorf("failed to register diagnostics channel: %w", err)
		}
	}

	// Add disposables to the application's disposable store
	app.AddDisposable(disposables)
	*/

	app.logService.Debug("IPC channels initialized successfully")
	return nil
}

// openFirstWindow opens the first application window
// Equivalent to openFirstWindow() in app.ts
func (app *CodeApplication) openFirstWindow(instantiationService instantiationcommon.IInstantiationService) error {
	app.logService.Debug("Opening first window...")

	// Get the windows main service using the service ID
	// First, create the service identifier if it doesn't exist
	IWindowsMainServiceID := instantiationcommon.CreateDecorator[windowsmain.IWindowsMainService]("windowsMainService")

	// Get the service using the instantiation service
	service, err := instantiationService.CreateInstance(IWindowsMainServiceID, nil)
	if err != nil {
		return fmt.Errorf("failed to get windows main service: %w", err)
	}

	// Type assert to IWindowsMainService
	windowsMainService, ok := service.(windowsmain.IWindowsMainService)
	if !ok {
		return fmt.Errorf("service is not of type IWindowsMainService")
	}

	// Note: Auxiliary windows service is not implemented yet
	app.logService.Debug("Auxiliary windows service not implemented yet")

	// Get command line arguments from environment service
	args := app.environmentMainService.Args()

	// Prepare window configuration with default values
	initialStartup := true
	windowConfig := &windowsmain.IOpenConfiguration{
		IBaseOpenConfiguration: windowsmain.IBaseOpenConfiguration{
			Context:         windowsmain.OpenContextDesktop,
			ContextWindowID: nil, // No parent window context
		},
		InitialStartup: &initialStartup,
	}

	// Set window open flags based on command line arguments
	// Using the boolean fields directly from NativeParsedArgs
	if args.NewWindow {
		newWindow := true
		windowConfig.ForceNewWindow = &newWindow
	}

	if args.ReuseWindow {
		reuseWindow := true
		windowConfig.ForceReuseWindow = &reuseWindow
	}

	// Check if we're in chat mode and set appropriate flags
	if args.Chat != nil {
		// Handle chat mode specific window configuration
		// TODO: Implement chat mode window configuration
	}

	// Set WaitMarkerFileURI if specified
	if args.WaitMarkerFilePath != "" {
		windowConfig.WaitMarkerFileURI = &args.WaitMarkerFilePath
	}

	// Handle folder and file URIs if specified
	if len(args.FolderURI) > 0 {
		// In a real implementation, we would handle folder URIs here
		// For now, we'll just log that we received them
		app.logService.Debug("Received folder URIs:", args.FolderURI)
	}

	if len(args.FileURI) > 0 {
		// In a real implementation, we would handle file URIs here
		// For now, we'll just log that we received them
		app.logService.Debug("Received file URIs:", args.FileURI)
	}

	// Handle other command line arguments as needed
	// This is a simplified implementation that just logs the arguments
	app.logService.Debug("Command line arguments:", args)

	// Open the window
	_, err = windowsMainService.Open(windowConfig)
	if err != nil {
		return fmt.Errorf("failed to open first window: %w", err)
	}

	app.logService.Debug("First window opened successfully")
	return nil
}

// afterWindowOpen performs post-window-open tasks
// Equivalent to afterWindowOpen() in app.ts
func (app *CodeApplication) afterWindowOpen() {
	app.logService.Debug("Performing post-window-open tasks...")

	// Background tasks that run after the window is open

	app.logService.Debug("Post-window-open tasks completed")
}

// Shutdown gracefully shuts down the application
func (app *CodeApplication) Shutdown() error {
	app.mutex.Lock()
	defer app.mutex.Unlock()

	app.logService.Debug("Shutting down VS Code application...")

	app.cancel()
	app.Dispose() // Call Disposable cleanup

	app.logService.Debug("VS Code application shut down completed")
	return nil
}

// Context returns the application context
func (app *CodeApplication) Context() context.Context {
	return app.ctx
}
