/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"container/list"
	"sync"
)

// GetOrSet gets a value from the map or sets it if it doesn't exist
func GetOrSet[K comparable, V any](m map[K]V, key K, value V) V {
	if result, exists := m[key]; exists {
		return result
	}
	m[key] = value
	return value
}

// MapToString returns a string representation of a map
func MapToString[K comparable, V any](m map[K]V) string {
	// Implementation would go here - simplified for now
	return "Map(...)"
}

// SetToString returns a string representation of a set
func SetToString[K comparable](s map[K]bool) string {
	// Implementation would go here - simplified for now
	return "Set(...)"
}

// ResourceMapEntry represents an entry in a ResourceMap
type ResourceMapEntry[T any] struct {
	URI   *URI
	Value T
}

// ResourceMap is a map that uses URI as keys with custom comparison
type ResourceMap[T any] struct {
	entries map[string]*ResourceMapEntry[T]
	toKey   func(*URI) string
}

// NewResourceMap creates a new ResourceMap
func NewResourceMap[T any](toKey ...func(*URI) string) *ResourceMap[T] {
	keyFn := func(uri *URI) string {
		return uri.ToString()
	}
	if len(toKey) > 0 && toKey[0] != nil {
		keyFn = toKey[0]
	}

	return &ResourceMap[T]{
		entries: make(map[string]*ResourceMapEntry[T]),
		toKey:   keyFn,
	}
}

// Set adds or updates an entry in the ResourceMap
func (rm *ResourceMap[T]) Set(uri *URI, value T) {
	key := rm.toKey(uri)
	rm.entries[key] = &ResourceMapEntry[T]{
		URI:   uri,
		Value: value,
	}
}

// Get retrieves a value from the ResourceMap
func (rm *ResourceMap[T]) Get(uri *URI) (T, bool) {
	key := rm.toKey(uri)
	if entry, exists := rm.entries[key]; exists {
		return entry.Value, true
	}
	var zero T
	return zero, false
}

// Has checks if a URI exists in the ResourceMap
func (rm *ResourceMap[T]) Has(uri *URI) bool {
	key := rm.toKey(uri)
	_, exists := rm.entries[key]
	return exists
}

// Delete removes an entry from the ResourceMap
func (rm *ResourceMap[T]) Delete(uri *URI) bool {
	key := rm.toKey(uri)
	if _, exists := rm.entries[key]; exists {
		delete(rm.entries, key)
		return true
	}
	return false
}

// Clear removes all entries from the ResourceMap
func (rm *ResourceMap[T]) Clear() {
	rm.entries = make(map[string]*ResourceMapEntry[T])
}

// Size returns the number of entries in the ResourceMap
func (rm *ResourceMap[T]) Size() int {
	return len(rm.entries)
}

// ForEach iterates over all entries in the ResourceMap
func (rm *ResourceMap[T]) ForEach(fn func(*URI, T)) {
	for _, entry := range rm.entries {
		fn(entry.URI, entry.Value)
	}
}

// Keys returns all URIs in the ResourceMap
func (rm *ResourceMap[T]) Keys() []*URI {
	keys := make([]*URI, 0, len(rm.entries))
	for _, entry := range rm.entries {
		keys = append(keys, entry.URI)
	}
	return keys
}

// Values returns all values in the ResourceMap
func (rm *ResourceMap[T]) Values() []T {
	values := make([]T, 0, len(rm.entries))
	for _, entry := range rm.entries {
		values = append(values, entry.Value)
	}
	return values
}

// Iterator returns an iterator for the ResourceMap
func (rm *ResourceMap[T]) Iterator() func() (*URI, T, bool) {
	entries := make([]*ResourceMapEntry[T], 0, len(rm.entries))
	for _, entry := range rm.entries {
		entries = append(entries, entry)
	}

	index := 0
	return func() (*URI, T, bool) {
		if index >= len(entries) {
			var zero T
			return nil, zero, false
		}
		entry := entries[index]
		index++
		return entry.URI, entry.Value, true
	}
}

// ResourceSet is a set that uses URI as keys with custom comparison
type ResourceSet struct {
	resourceMap *ResourceMap[*URI]
}

// NewResourceSet creates a new ResourceSet
func NewResourceSet(toKey func(*URI) string) *ResourceSet {
	return &ResourceSet{
		resourceMap: NewResourceMap[*URI](toKey),
	}
}

// Add adds a URI to the set
func (rs *ResourceSet) Add(uri *URI) {
	rs.resourceMap.Set(uri, uri)
}

// Has checks if a URI exists in the set
func (rs *ResourceSet) Has(uri *URI) bool {
	return rs.resourceMap.Has(uri)
}

// Delete removes a URI from the set
func (rs *ResourceSet) Delete(uri *URI) bool {
	return rs.resourceMap.Delete(uri)
}

// Clear removes all URIs from the set
func (rs *ResourceSet) Clear() {
	rs.resourceMap.Clear()
}

// Size returns the number of URIs in the set
func (rs *ResourceSet) Size() int {
	return rs.resourceMap.Size()
}

// ForEach iterates over all URIs in the set
func (rs *ResourceSet) ForEach(fn func(*URI)) {
	rs.resourceMap.ForEach(func(uri *URI, value *URI) {
		fn(value)
	})
}

// Keys returns all URIs in the set
func (rs *ResourceSet) Keys() []*URI {
	return rs.resourceMap.Values()
}

type LRUCache[K comparable, V any] struct {
	capacity int
	list     *list.List
	elements map[K]*list.Element
	mutex    sync.Mutex
}

type entry[K comparable, V any] struct {
	key   K
	value V
}

func NewLRUCache[K comparable, V any](capacity int) *LRUCache[K, V] {
	return &LRUCache[K, V]{
		capacity: capacity,
		list:     list.New(),
		elements: make(map[K]*list.Element),
	}
}

func (c *LRUCache[K, V]) Get(key K) (V, bool) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if elem, ok := c.elements[key]; ok {
		c.list.MoveToFront(elem)
		return elem.Value.(*entry[K, V]).value, true
	}
	var zero V
	return zero, false
}

func (c *LRUCache[K, V]) Set(key K, value V) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if elem, ok := c.elements[key]; ok {
		c.list.MoveToFront(elem)
		elem.Value.(*entry[K, V]).value = value
		return
	}

	if c.list.Len() >= c.capacity {
		oldest := c.list.Back()
		if oldest != nil {
			delete(c.elements, oldest.Value.(*entry[K, V]).key)
			c.list.Remove(oldest)
		}
	}

	elem := c.list.PushFront(&entry[K, V]{key, value})
	c.elements[key] = elem
}

func (c *LRUCache[K, V]) Delete(key K) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if elem, ok := c.elements[key]; ok {
		delete(c.elements, key)
		c.list.Remove(elem)
	}
}

func (c *LRUCache[K, V]) Len() int {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	return c.list.Len()
}
