/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

// JSONSchemaType represents the type of a JSON schema
type JSONSchemaType string

const (
	JSONSchemaTypeString  JSONSchemaType = "string"
	JSONSchemaTypeNumber  JSONSchemaType = "number"
	JSONSchemaTypeInteger JSONSchemaType = "integer"
	JSONSchemaTypeBoolean JSONSchemaType = "boolean"
	JSONSchemaTypeObject  JSONSchemaType = "object"
	JSONSchemaTypeArray   JSONSchemaType = "array"
	JSONSchemaTypeNull    JSONSchemaType = "null"
)

// IJSONSchemaMap represents a map of JSON schemas
type IJSONSchemaMap map[string]*IJSONSchema

// IJSONSchemaSnippet represents a JSON schema snippet
type IJSONSchemaSnippet struct {
	Label       string      `json:"label,omitempty"`
	Description string      `json:"description,omitempty"`
	Body        interface{} `json:"body,omitempty"`
}

// IJSONSchema represents a JSON schema
type IJSONSchema struct {
	ID                   *string                `json:"id,omitempty"`
	Schema               *string                `json:"$schema,omitempty"`
	Type                 interface{}            `json:"type,omitempty"` // JSONSchemaType or []JSONSchemaType
	Title                *string                `json:"title,omitempty"`
	Default              interface{}            `json:"default,omitempty"`
	Definitions          IJSONSchemaMap         `json:"definitions,omitempty"`
	Description          *string                `json:"description,omitempty"`
	Properties           IJSONSchemaMap         `json:"properties,omitempty"`
	PatternProperties    IJSONSchemaMap         `json:"patternProperties,omitempty"`
	AdditionalProperties interface{}            `json:"additionalProperties,omitempty"` // bool or *IJSONSchema
	MinProperties        *int                   `json:"minProperties,omitempty"`
	MaxProperties        *int                   `json:"maxProperties,omitempty"`
	Dependencies         interface{}            `json:"dependencies,omitempty"` // IJSONSchemaMap or map[string][]string
	Items                interface{}            `json:"items,omitempty"`        // *IJSONSchema or []*IJSONSchema
	MinItems             *int                   `json:"minItems,omitempty"`
	MaxItems             *int                   `json:"maxItems,omitempty"`
	UniqueItems          *bool                  `json:"uniqueItems,omitempty"`
	AdditionalItems      interface{}            `json:"additionalItems,omitempty"` // bool or *IJSONSchema
	Pattern              *string                `json:"pattern,omitempty"`
	MinLength            *int                   `json:"minLength,omitempty"`
	MaxLength            *int                   `json:"maxLength,omitempty"`
	Minimum              *float64               `json:"minimum,omitempty"`
	Maximum              *float64               `json:"maximum,omitempty"`
	ExclusiveMinimum     interface{}            `json:"exclusiveMinimum,omitempty"` // bool or float64
	ExclusiveMaximum     interface{}            `json:"exclusiveMaximum,omitempty"` // bool or float64
	MultipleOf           *float64               `json:"multipleOf,omitempty"`
	Required             []string               `json:"required,omitempty"`
	Ref                  *string                `json:"$ref,omitempty"`
	AnyOf                []*IJSONSchema         `json:"anyOf,omitempty"`
	AllOf                []*IJSONSchema         `json:"allOf,omitempty"`
	OneOf                []*IJSONSchema         `json:"oneOf,omitempty"`
	Not                  *IJSONSchema           `json:"not,omitempty"`
	Enum                 []interface{}          `json:"enum,omitempty"`
	Format               *string                `json:"format,omitempty"`

	// Schema draft 06
	Const        interface{}    `json:"const,omitempty"`
	Contains     *IJSONSchema   `json:"contains,omitempty"`
	PropertyNames *IJSONSchema  `json:"propertyNames,omitempty"`
	Examples     []interface{}  `json:"examples,omitempty"`

	// Schema draft 07
	Comment *string      `json:"$comment,omitempty"`
	If      *IJSONSchema `json:"if,omitempty"`
	Then    *IJSONSchema `json:"then,omitempty"`
	Else    *IJSONSchema `json:"else,omitempty"`

	// Schema 2019-09
	UnevaluatedProperties interface{}            `json:"unevaluatedProperties,omitempty"` // bool or *IJSONSchema
	UnevaluatedItems      interface{}            `json:"unevaluatedItems,omitempty"`      // bool or *IJSONSchema
	MinContains           *int                   `json:"minContains,omitempty"`
	MaxContains           *int                   `json:"maxContains,omitempty"`
	Deprecated            *bool                  `json:"deprecated,omitempty"`
	DependentRequired     map[string][]string    `json:"dependentRequired,omitempty"`
	DependentSchemas      IJSONSchemaMap         `json:"dependentSchemas,omitempty"`
	Defs                  map[string]*IJSONSchema `json:"$defs,omitempty"`
	Anchor                *string                `json:"$anchor,omitempty"`
	RecursiveRef          *string                `json:"$recursiveRef,omitempty"`
	RecursiveAnchor       *string                `json:"$recursiveAnchor,omitempty"`
	Vocabulary            interface{}            `json:"$vocabulary,omitempty"`

	// Schema 2020-12
	PrefixItems   []*IJSONSchema `json:"prefixItems,omitempty"`
	DynamicRef    *string        `json:"$dynamicRef,omitempty"`
	DynamicAnchor *string        `json:"$dynamicAnchor,omitempty"`

	// VSCode extensions
	DefaultSnippets           []*IJSONSchemaSnippet `json:"defaultSnippets,omitempty"`
	ErrorMessage              *string               `json:"errorMessage,omitempty"`
	PatternErrorMessage       *string               `json:"patternErrorMessage,omitempty"`
	DeprecationMessage        *string               `json:"deprecationMessage,omitempty"`
	MarkdownDeprecationMessage *string              `json:"markdownDeprecationMessage,omitempty"`
	EnumDescriptions          []string              `json:"enumDescriptions,omitempty"`
	MarkdownEnumDescriptions  []string              `json:"markdownEnumDescriptions,omitempty"`
	MarkdownDescription       *string               `json:"markdownDescription,omitempty"`
	DoNotSuggest              *bool                 `json:"doNotSuggest,omitempty"`
	SuggestSortText           *string               `json:"suggestSortText,omitempty"`
	AllowComments             *bool                 `json:"allowComments,omitempty"`
	AllowTrailingCommas       *bool                 `json:"allowTrailingCommas,omitempty"`
}
