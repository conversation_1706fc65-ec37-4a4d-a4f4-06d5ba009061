package common

import (
	"net/url"
	"strings"
)

// Schemas contains well-known URI schemes.
var Schemas = struct {
	InMemory                       string
	Vscode                         string
	Internal                       string
	WalkThrough                    string
	WalkThroughSnippet             string
	HTTP                           string
	HTTPS                          string
	File                           string
	Mailto                         string
	Untitled                       string
	Data                           string
	Command                        string
	VscodeRemote                   string
	VscodeRemoteResource           string
	VscodeManagedRemoteResource    string
	VscodeUserData                 string
	VscodeCustomEditor             string
	VscodeNotebookCell             string
	VscodeNotebookCellMetadata     string
	VscodeNotebookCellMetadataDiff string
	VscodeNotebookCellOutput       string
	VscodeNotebookCellOutputDiff   string
	VscodeNotebookMetadata         string
	VscodeInteractiveInput         string
	VscodeSettings                 string
	VscodeWorkspaceTrust           string
	VscodeTerminal                 string
	VscodeChatCodeBlock            string
	VscodeChatCodeCompareBlock     string
	VscodeChatSesssion             string
	VscodeChatInput                string
	WebviewPanel                   string
	VscodeWebview                  string
	Extension                      string
	VscodeFileResource             string
	Tmp                            string
	Vsls                           string
	VscodeSourceControl            string
	CommentsInput                  string
	CodeSetting                    string
	OutputChannel                  string
	AccessibleView                 string
}{
	InMemory:                       "inmemory",
	Vscode:                         "vscode",
	Internal:                       "private",
	WalkThrough:                    "walkThrough",
	WalkThroughSnippet:             "walkThroughSnippet",
	HTTP:                           "http",
	HTTPS:                          "https",
	File:                           "file",
	Mailto:                         "mailto",
	Untitled:                       "untitled",
	Data:                           "data",
	Command:                        "command",
	VscodeRemote:                   "vscode-remote",
	VscodeRemoteResource:           "vscode-remote-resource",
	VscodeManagedRemoteResource:    "vscode-managed-remote-resource",
	VscodeUserData:                 "vscode-userdata",
	VscodeCustomEditor:             "vscode-custom-editor",
	VscodeNotebookCell:             "vscode-notebook-cell",
	VscodeNotebookCellMetadata:     "vscode-notebook-cell-metadata",
	VscodeNotebookCellMetadataDiff: "vscode-notebook-cell-metadata-diff",
	VscodeNotebookCellOutput:       "vscode-notebook-cell-output",
	VscodeNotebookCellOutputDiff:   "vscode-notebook-cell-output-diff",
	VscodeNotebookMetadata:         "vscode-notebook-metadata",
	VscodeInteractiveInput:         "vscode-interactive-input",
	VscodeSettings:                 "vscode-settings",
	VscodeWorkspaceTrust:           "vscode-workspace-trust",
	VscodeTerminal:                 "vscode-terminal",
	VscodeChatCodeBlock:            "vscode-chat-code-block",
	VscodeChatCodeCompareBlock:     "vscode-chat-code-compare-block",
	VscodeChatSesssion:             "vscode-chat-editor",
	VscodeChatInput:                "chatSessionInput",
	WebviewPanel:                   "webview-panel",
	VscodeWebview:                  "vscode-webview",
	Extension:                      "extension",
	VscodeFileResource:             "vscode-file",
	Tmp:                            "tmp",
	Vsls:                           "vsls",
	VscodeSourceControl:            "vscode-scm",
	CommentsInput:                  "comment",
	CodeSetting:                    "code-setting",
	OutputChannel:                  "output",
	AccessibleView:                 "accessible-view",
}

// FileAccess defines helpers for file access.
var FileAccess = &FileAccessImpl{}

// FileAccessImpl provides implementation for file access helpers.
type FileAccessImpl struct{}

const VSCODE_AUTHORITY = "vscode-app"

// AsFileUri converts a resource path to a file URI.
func (f *FileAccessImpl) AsFileUri(resourcePath string) *URI {
	return f.uriToFileUri(f.toUri(resourcePath))
}

// uriToFileUri converts a URI to a file URI.
func (f *FileAccessImpl) uriToFileUri(uri *URI) *URI {
	if uri.Scheme == Schemas.VscodeFileResource {
		authority := uri.Authority
		if authority == VSCODE_AUTHORITY {
			authority = ""
		}
		return uri.With(UriComponents{
			Scheme:    Schemas.File,
			Authority: authority,
			Query:     "",
			Fragment:  "",
		})
	}
	return uri
}

// toUri converts a URI or module path to a URI.
func (f *FileAccessImpl) toUri(uriOrModule string) *URI {
	// In a real implementation, this would handle module paths and roots.
	// For now, we'll just parse it as a URI.
	uri := ParseURI(uriOrModule)
	return uri
}

// AppResourcePath represents a path inside the app.
type AppResourcePath string

// BuiltinExtensionsPath is the path to the built-in extensions.
const BuiltinExtensionsPath AppResourcePath = "vs/../../extensions"

// NodeModulesPath is the path to the node_modules directory.
const NodeModulesPath AppResourcePath = "vs/../../node_modules"

// NodeModulesAsarPath is the path to the node_modules.asar file.
const NodeModulesAsarPath AppResourcePath = "vs/../../node_modules.asar"

// NodeModulesAsarUnpackedPath is the path to the unpacked node_modules.asar directory.
const NodeModulesAsarUnpackedPath AppResourcePath = "vs/../../node_modules.asar.unpacked"

// MatchesScheme checks if a URI or string matches a scheme.
func MatchesScheme(target interface{}, scheme string) bool {
	if uri, ok := target.(*URI); ok {
		return strings.EqualFold(uri.Scheme, scheme)
	}
	if str, ok := target.(string); ok {
		return strings.HasPrefix(strings.ToLower(str), strings.ToLower(scheme)+":")
	}
	return false
}

// MatchesSomeScheme checks if a URI or string matches one of several schemes.
func MatchesSomeScheme(target interface{}, schemes ...string) bool {
	for _, scheme := range schemes {
		if MatchesScheme(target, scheme) {
			return true
		}
	}
	return false
}

// ConnectionTokenCookieName is the name of the connection token cookie
const ConnectionTokenCookieName = "vscode-tkn"

// ConnectionTokenQueryName is the name of the connection token query parameter
const ConnectionTokenQueryName = "tkn"

// RemoteAuthorities manages remote authority configurations
type RemoteAuthorities struct {
	hosts              map[string]string
	ports              map[string]int
	connectionTokens   map[string]string
	preferredWebSchema string
	delegate           func(*URI) *URI
	serverRootPath     string
}

// NewRemoteAuthorities creates a new RemoteAuthorities instance
func NewRemoteAuthorities() *RemoteAuthorities {
	return &RemoteAuthorities{
		hosts:              make(map[string]string),
		ports:              make(map[string]int),
		connectionTokens:   make(map[string]string),
		preferredWebSchema: "http",
		serverRootPath:     "/",
	}
}

// SetPreferredWebSchema sets the preferred web schema
func (ra *RemoteAuthorities) SetPreferredWebSchema(schema string) {
	ra.preferredWebSchema = schema
}

// SetDelegate sets the URI rewrite delegate
func (ra *RemoteAuthorities) SetDelegate(delegate func(*URI) *URI) {
	ra.delegate = delegate
}

// SetServerRootPath sets the server root path
func (ra *RemoteAuthorities) SetServerRootPath(product map[string]interface{}, serverBasePath string) {
	// In a real implementation, this would calculate the path based on product info
	if serverBasePath == "" {
		serverBasePath = "/"
	}
	ra.serverRootPath = serverBasePath
}

// GetServerRootPath returns the server root path
func (ra *RemoteAuthorities) GetServerRootPath() string {
	return ra.serverRootPath
}

// Set sets the host and port for an authority
func (ra *RemoteAuthorities) Set(authority, host string, port int) {
	ra.hosts[authority] = host
	ra.ports[authority] = port
}

// SetConnectionToken sets the connection token for an authority
func (ra *RemoteAuthorities) SetConnectionToken(authority, connectionToken string) {
	ra.connectionTokens[authority] = connectionToken
}

// GetPreferredWebSchema returns the preferred web schema
func (ra *RemoteAuthorities) GetPreferredWebSchema() string {
	return ra.preferredWebSchema
}

// Rewrite rewrites a URI using the delegate if available
func (ra *RemoteAuthorities) Rewrite(uri *URI) *URI {
	if ra.delegate != nil {
		return ra.delegate(uri)
	}
	return uri
}

// COI provides Cross-Origin Isolation utilities
type COI struct{}

// GetHeadersFromQuery extracts headers from a query string
func (coi *COI) GetHeadersFromQuery(urlStr string) (map[string]string, error) {
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return nil, err
	}

	headers := make(map[string]string)
	values := parsedURL.Query()

	for key, vals := range values {
		if len(vals) > 0 {
			headers[key] = vals[0]
		}
	}

	return headers, nil
}

// AddSearchParam adds search parameters for COOP/COEP
func (coi *COI) AddSearchParam(params map[string]string, coopValue, coepValue bool) {
	if coopValue {
		params["coop"] = "true"
	}
	if coepValue {
		params["coep"] = "true"
	}
}
