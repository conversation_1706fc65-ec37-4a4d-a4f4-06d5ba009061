package common

import "sync"

// Barrier is a synchronization primitive that blocks until it is opened.
// Once opened, it remains open.
type Barrier struct {
	mu     sync.Mutex
	opened bool
	ch     chan struct{}
}

// NewBarrier creates a new Barrier.
func NewBarrier() *Barrier {
	return &Barrier{
		ch: make(chan struct{}),
	}
}

// Open opens the barrier, allowing all waiting goroutines to proceed.
func (b *Barrier) Open() {
	b.mu.Lock()
	defer b.mu.Unlock()

	if !b.opened {
		b.opened = true
		close(b.ch)
	}
}

// Wait returns a channel that is closed when the barrier is opened.
func (b *Barrier) Wait() <-chan struct{} {
	return b.ch
}
