package common

import (
	"os"
	"runtime"
	"strings"
)

// Cwd provides safe access to the `cwd` property.
// It checks for the VSCODE_CWD environment variable first,
// then falls back to the process's current working directory.
var Cwd = func() string {
	if vscodeCwd := os.Getenv("VSCODE_CWD"); vscodeCwd != "" {
		return vscodeCwd
	}
	cwd, err := os.Getwd()
	if err != nil {
		return "/" // Fallback to root
	}
	return cwd
}()

// Env provides safe access to the `env` property.
var Env = func() map[string]string {
	envMap := make(map[string]string)
	for _, e := range os.Environ() {
		if i := strings.Index(e, "="); i >= 0 {
			envMap[e[:i]] = e[i+1:]
		}
	}
	return envMap
}()

// Arch provides safe access to the `arch` property.
var Arch = runtime.GOARCH
