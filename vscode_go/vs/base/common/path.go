/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

// NOTE: VS Code's copy of nodejs path library to be usable in common (non-node) namespace
// Translated from TypeScript to Go

package common

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
)

// Character constants
const (
	CharUppercaseA    = 65  // A
	CharLowercaseA    = 97  // a
	CharUppercaseZ    = 90  // Z
	CharLowercaseZ    = 122 // z
	CharDot           = 46  // .
	CharForwardSlash  = 47  // /
	CharBackwardSlash = 92  // \
	CharColon         = 58  // :
	CharQuestionMark  = 63  // ?
)

// ErrorInvalidArgType represents an invalid argument type error
type ErrorInvalidArgType struct {
	Name     string
	Expected string
	Actual   interface{}
	Code     string
}

func (e *ErrorInvalidArgType) Error() string {
	determiner := "must be"
	expected := e.Expected
	if strings.HasPrefix(expected, "not ") {
		determiner = "must not be"
		expected = strings.TrimPrefix(expected, "not ")
	}

	argType := "argument"
	if strings.Contains(e.Name, ".") {
		argType = "property"
	}

	return fmt.Sprintf(`The "%s" %s %s of type %s. Received type %T`,
		e.Name, argType, determiner, expected, e.Actual)
}

// validateString validates that a value is a string
func validateString(value interface{}, name string) error {
	if _, ok := value.(string); !ok {
		return &ErrorInvalidArgType{
			Name:     name,
			Expected: "string",
			Actual:   value,
			Code:     "ERR_INVALID_ARG_TYPE",
		}
	}
	return nil
}

// validateObject validates that a value is an object
func validateObject(value interface{}, name string) error {
	if value == nil {
		return &ErrorInvalidArgType{
			Name:     name,
			Expected: "Object",
			Actual:   value,
			Code:     "ERR_INVALID_ARG_TYPE",
		}
	}
	return nil
}

// ParsedPath represents a parsed path object
type ParsedPath struct {
	Root string `json:"root"`
	Dir  string `json:"dir"`
	Base string `json:"base"`
	Ext  string `json:"ext"`
	Name string `json:"name"`
}

// IPath interface defines path manipulation methods
type IPath interface {
	Normalize(path string) string
	IsAbsolute(path string) bool
	Join(paths ...string) string
	Resolve(pathSegments ...string) string
	Relative(from, to string) string
	Dirname(path string) string
	Basename(path string, suffix ...string) string
	Extname(path string) string
	Format(pathObject ParsedPath) string
	Parse(path string) ParsedPath
	ToNamespacedPath(path string) string
	Sep() string
	Delimiter() string
}

// Global platform detection
var platformIsWin32 = PlatformVar == Windows

// isPathSeparator checks if a character code is a path separator
func isPathSeparator(code byte) bool {
	return code == CharForwardSlash || code == CharBackwardSlash
}

// isPosixPathSeparator checks if a character code is a POSIX path separator
func isPosixPathSeparator(code byte) bool {
	return code == CharForwardSlash
}

// isWindowsDeviceRoot checks if a character code is a Windows device root
func isWindowsDeviceRoot(code byte) bool {
	return (code >= CharUppercaseA && code <= CharUppercaseZ) ||
		(code >= CharLowercaseA && code <= CharLowercaseZ)
}

// normalizeString resolves . and .. elements in a path with directory names
func normalizeString(path string, allowAboveRoot bool, separator string, isPathSeparatorFunc func(byte) bool) string {
	res := ""
	lastSegmentLength := 0
	lastSlash := -1
	dots := 0
	var code byte

	for i := 0; i <= len(path); i++ {
		if i < len(path) {
			code = path[i]
		} else if isPathSeparatorFunc(code) {
			break
		} else {
			code = CharForwardSlash
		}

		if isPathSeparatorFunc(code) {
			if lastSlash == i-1 || dots == 1 {
				// NOOP
			} else if dots == 2 {
				if len(res) < 2 || lastSegmentLength != 2 ||
					(len(res) > 0 && res[len(res)-1] != '.') ||
					(len(res) > 1 && res[len(res)-2] != '.') {
					if len(res) > 2 {
						lastSlashIndex := strings.LastIndex(res, separator)
						if lastSlashIndex == -1 {
							res = ""
							lastSegmentLength = 0
						} else {
							res = res[:lastSlashIndex]
							lastSegmentLength = len(res) - 1 - strings.LastIndex(res, separator)
						}
						lastSlash = i
						dots = 0
						continue
					} else if len(res) != 0 {
						res = ""
						lastSegmentLength = 0
						lastSlash = i
						dots = 0
						continue
					}
				}
				if allowAboveRoot {
					if len(res) > 0 {
						res += separator + ".."
					} else {
						res = ".."
					}
					lastSegmentLength = 2
				}
			} else {
				if len(res) > 0 {
					res += separator + path[lastSlash+1:i]
				} else {
					res = path[lastSlash+1 : i]
				}
				lastSegmentLength = i - lastSlash - 1
			}
			lastSlash = i
			dots = 0
		} else if code == CharDot && dots != -1 {
			dots++
		} else {
			dots = -1
		}
	}
	return res
}

// formatExt formats an extension string
func formatExt(ext string) string {
	if ext == "" {
		return ""
	}
	if ext[0] == '.' {
		return ext
	}
	return "." + ext
}

// format formats a path object into a path string
func format(sep string, pathObject ParsedPath) string {
	dir := pathObject.Dir
	if dir == "" {
		dir = pathObject.Root
	}

	base := pathObject.Base
	if base == "" {
		base = pathObject.Name + formatExt(pathObject.Ext)
	}

	if dir == "" {
		return base
	}

	if dir == pathObject.Root {
		return dir + base
	}

	return dir + sep + base
}

// Win32Path implements IPath for Windows
type Win32Path struct{}

// Resolve resolves path segments to an absolute path for Windows
func (w *Win32Path) Resolve(pathSegments ...string) string {
	resolvedDevice := ""
	resolvedTail := ""
	resolvedAbsolute := false

	for i := len(pathSegments) - 1; i >= -1; i-- {
		var path string
		if i >= 0 {
			path = pathSegments[i]
			if err := validateString(path, fmt.Sprintf("paths[%d]", i)); err != nil {
				panic(err)
			}
			if path == "" {
				continue
			}
		} else if resolvedDevice == "" {
			path, _ = os.Getwd()
		} else {
			path = os.Getenv("=" + resolvedDevice)
			if path == "" {
				path, _ = os.Getwd()
			}

			if strings.ToLower(path[:2]) != strings.ToLower(resolvedDevice) || path[2] == '\\' {
				path = resolvedDevice + "\\"
			}
		}

		length := len(path)
		rootEnd := 0
		device := ""
		isAbsolute := false
		code := path[0]

		if length > 0 {
			if isPathSeparator(code) {
				isAbsolute = true
				if isPathSeparator(path[1]) {
					j := 2
					last := j
					for j < length && !isPathSeparator(path[j]) {
						j++
					}
					if j < length && j != last {
						firstPart := path[last:j]
						last = j
						for j < length && isPathSeparator(path[j]) {
							j++
						}
						if j < length && j != last {
							last = j
							for j < length && !isPathSeparator(path[j]) {
								j++
							}
							if j == length || j != last {
								device = "\\\\" + firstPart + "\\" + path[last:j]
								rootEnd = j
							}
						}
					}
				} else {
					rootEnd = 1
				}
			} else if isWindowsDeviceRoot(code) && path[1] == ':' {
				device = path[:2]
				rootEnd = 2
				if length > 2 && isPathSeparator(path[2]) {
					isAbsolute = true
					rootEnd = 3
				}
			}
		}

		if device != "" {
			if resolvedDevice != "" {
				if strings.ToLower(device) != strings.ToLower(resolvedDevice) {
					continue
				}
			} else {
				resolvedDevice = device
			}
		}

		if resolvedAbsolute {
			if resolvedDevice != "" {
				break
			}
		} else {
			resolvedTail = path[rootEnd:] + "\\" + resolvedTail
			resolvedAbsolute = isAbsolute
			if isAbsolute && resolvedDevice != "" {
				break
			}
		}
	}

	resolvedTail = normalizeString(resolvedTail, !resolvedAbsolute, "\\", isPathSeparator)

	if resolvedAbsolute {
		return resolvedDevice + "\\" + resolvedTail
	}
	result := resolvedDevice + resolvedTail
	if result == "" {
		return "."
	}
	return result
}

// Normalize normalizes a Windows path
func (w *Win32Path) Normalize(path string) string {
	if err := validateString(path, "path"); err != nil {
		panic(err)
	}
	length := len(path)
	if length == 0 {
		return "."
	}
	rootEnd := 0
	var device string
	isAbsolute := false
	code := path[0]

	if length > 1 {
		if isPathSeparator(code) {
			isAbsolute = true
			if isPathSeparator(path[1]) {
				j := 2
				last := j
				for j < length && !isPathSeparator(path[j]) {
					j++
				}
				if j < length && j != last {
					firstPart := path[last:j]
					last = j
					for j < length && isPathSeparator(path[j]) {
						j++
					}
					if j < length && j != last {
						last = j
						for j < length && !isPathSeparator(path[j]) {
							j++
						}
						if j == length {
							return "\\\\" + firstPart + "\\" + path[last:] + "\\"
						} else if j != last {
							device = "\\\\" + firstPart + "\\" + path[last:j]
							rootEnd = j
						}
					}
				}
			} else {
				rootEnd = 1
			}
		} else if isWindowsDeviceRoot(code) && path[1] == ':' {
			device = path[:2]
			rootEnd = 2
			if length > 2 && isPathSeparator(path[2]) {
				isAbsolute = true
				rootEnd = 3
			}
		}
	}

	tail := ""
	if rootEnd < length {
		tail = normalizeString(path[rootEnd:], !isAbsolute, "\\", isPathSeparator)
	}
	if tail == "" && !isAbsolute {
		tail = "."
	}
	if tail != "" && isPathSeparator(path[length-1]) {
		tail += "\\"
	}

	return device + tail
}

// IsAbsolute checks if a path is absolute on Windows
func (w *Win32Path) IsAbsolute(path string) bool {
	if err := validateString(path, "path"); err != nil {
		panic(err)
	}
	length := len(path)
	if length == 0 {
		return false
	}
	code := path[0]
	return isPathSeparator(code) || (length > 2 && isWindowsDeviceRoot(code) && path[1] == ':' && isPathSeparator(path[2]))
}

// Join joins path segments for Windows
func (w *Win32Path) Join(paths ...string) string {
	if len(paths) == 0 {
		return "."
	}

	var joined string
	var firstPart string

	for _, path := range paths {
		if err := validateString(path, "path"); err != nil {
			panic(err)
		}
		if path != "" {
			if joined == "" {
				joined = path
				firstPart = path
			} else {
				joined += "\\" + path
			}
		}
	}

	if joined == "" {
		return "."
	}

	isAbsolute := w.IsAbsolute(firstPart)
	if !isAbsolute {
		return w.Normalize(joined)
	}

	return w.Normalize(joined)
}

// Relative calculates the relative path from 'from' to 'to' on Windows
func (w *Win32Path) Relative(from, to string) string {
	if err := validateString(from, "from"); err != nil {
		panic(err)
	}
	if err := validateString(to, "to"); err != nil {
		panic(err)
	}

	if from == to {
		return ""
	}

	fromAbs := w.Resolve(from)
	toAbs := w.Resolve(to)

	if fromAbs == toAbs {
		return ""
	}

	fromLower := strings.ToLower(fromAbs)
	toLower := strings.ToLower(toAbs)

	if fromLower == toLower {
		return ""
	}

	fromDevice := ""
	fromStart := 0
	if len(fromLower) > 1 && fromLower[1] == ':' {
		fromDevice = fromLower[:2]
		fromStart = 2
	} else if len(fromLower) > 1 && fromLower[0] == '\\' && fromLower[1] == '\\' {
		fromStart = 2
		fromEnd := strings.Index(fromLower[fromStart:], "\\")
		if fromEnd == -1 {
			fromEnd = len(fromLower)
		}
		fromDevice = fromLower[:fromStart+fromEnd]
		fromStart = fromStart + fromEnd
	}

	toDevice := ""
	toStart := 0
	if len(toLower) > 1 && toLower[1] == ':' {
		toDevice = toLower[:2]
		toStart = 2
	} else if len(toLower) > 1 && toLower[0] == '\\' && toLower[1] == '\\' {
		toStart = 2
		toEnd := strings.Index(toLower[toStart:], "\\")
		if toEnd == -1 {
			toEnd = len(toLower)
		}
		toDevice = toLower[:toStart+toEnd]
		toStart = toStart + toEnd
	}

	if fromDevice != toDevice {
		return toAbs
	}

	fromSegs := strings.Split(fromAbs[fromStart:], "\\")
	toSegs := strings.Split(toAbs[toStart:], "\\")

	commonCount := 0
	for i := 0; i < len(fromSegs) && i < len(toSegs); i++ {
		if strings.ToLower(fromSegs[i]) == strings.ToLower(toSegs[i]) {
			commonCount++
		} else {
			break
		}
	}

	if commonCount == 0 {
		return toAbs
	}

	var relPath []string
	for i := commonCount; i < len(fromSegs); i++ {
		relPath = append(relPath, "..")
	}
	relPath = append(relPath, toSegs[commonCount:]...)

	return strings.Join(relPath, "\\")
}

// Dirname returns the directory name of a path on Windows
func (w *Win32Path) Dirname(path string) string {
	if err := validateString(path, "path"); err != nil {
		panic(err)
	}

	if len(path) == 0 {
		return "."
	}

	dir := filepath.Dir(path)
	if platformIsWin32 {
		dir = strings.ReplaceAll(dir, "/", "\\")
	}

	return dir
}

// Basename returns the last portion of a path on Windows
func (w *Win32Path) Basename(path string, suffix ...string) string {
	if err := validateString(path, "path"); err != nil {
		panic(err)
	}

	base := filepath.Base(path)

	if len(suffix) > 0 && suffix[0] != "" {
		if err := validateString(suffix[0], "suffix"); err != nil {
			panic(err)
		}
		if strings.HasSuffix(base, suffix[0]) {
			base = base[:len(base)-len(suffix[0])]
		}
	}

	return base
}

// Extname returns the extension of a path on Windows
func (w *Win32Path) Extname(path string) string {
	if err := validateString(path, "path"); err != nil {
		panic(err)
	}

	return filepath.Ext(path)
}

// Format formats a path object into a Windows path string
func (w *Win32Path) Format(pathObject ParsedPath) string {
	if err := validateObject(pathObject, "pathObject"); err != nil {
		panic(err)
	}

	return format("\\", pathObject)
}

// Parse parses a Windows path into its components
func (w *Win32Path) Parse(path string) ParsedPath {
	if err := validateString(path, "path"); err != nil {
		panic(err)
	}

	ret := ParsedPath{Root: "", Dir: "", Base: "", Ext: "", Name: ""}
	if len(path) == 0 {
		return ret
	}

	// Simplified parsing using Go's filepath functions
	ret.Dir = filepath.Dir(path)
	ret.Base = filepath.Base(path)
	ret.Ext = filepath.Ext(path)
	ret.Name = strings.TrimSuffix(ret.Base, ret.Ext)

	// Determine root
	if w.IsAbsolute(path) {
		if len(path) >= 2 && path[1] == ':' {
			ret.Root = path[:2] + "\\"
		} else if strings.HasPrefix(path, "\\\\") {
			// UNC path
			parts := strings.Split(path, "\\")
			if len(parts) >= 4 {
				ret.Root = "\\\\" + parts[2] + "\\" + parts[3] + "\\"
			}
		} else {
			ret.Root = "\\"
		}
	}

	return ret
}

// ToNamespacedPath converts a path to a namespaced path on Windows
func (w *Win32Path) ToNamespacedPath(path string) string {
	if len(path) == 0 {
		return path
	}

	resolvedPath := w.Resolve(path)

	if len(resolvedPath) <= 2 {
		return path
	}

	if resolvedPath[0] == '\\' {
		// Possible UNC root
		if resolvedPath[1] == '\\' {
			code := resolvedPath[2]
			if code != '?' && code != '.' {
				// Matched non-long UNC root, convert the path to a long UNC path
				return "\\\\?\\UNC\\" + resolvedPath[2:]
			}
		}
	} else if isWindowsDeviceRoot(resolvedPath[0]) &&
		resolvedPath[1] == ':' &&
		resolvedPath[2] == '\\' {
		// Matched device root, convert the path to a long UNC path
		return "\\\\?\\" + resolvedPath
	}

	return resolvedPath
}

// Sep returns the path separator for Windows
func (w *Win32Path) Sep() string {
	return "\\"
}

// Delimiter returns the path delimiter for Windows
func (w *Win32Path) Delimiter() string {
	return ";"
}

// PosixPath implements IPath for POSIX
type PosixPath struct{}

// Resolve resolves path segments to an absolute path for POSIX
func (p *PosixPath) Resolve(pathSegments ...string) string {
	resolvedPath := ""
	resolvedAbsolute := false

	for i := len(pathSegments) - 1; i >= -1 && !resolvedAbsolute; i-- {
		var path string
		if i >= 0 {
			path = pathSegments[i]
		} else {
			path, _ = os.Getwd()
		}

		if err := validateString(path, "path"); err != nil {
			panic(err)
		}

		if path == "" {
			continue
		}

		resolvedPath = path + "/" + resolvedPath
		resolvedAbsolute = path[0] == '/'
	}

	resolvedPath = normalizeString(resolvedPath, !resolvedAbsolute, "/", isPosixPathSeparator)

	if resolvedAbsolute {
		return "/" + resolvedPath
	}
	if resolvedPath != "" {
		return resolvedPath
	}
	return "."
}

// Normalize normalizes a POSIX path
func (p *PosixPath) Normalize(path string) string {
	if err := validateString(path, "path"); err != nil {
		panic(err)
	}

	if path == "" {
		return "."
	}

	isAbsolute := p.IsAbsolute(path)
	trailingSeparator := path[len(path)-1] == '/'

	path = normalizeString(path, !isAbsolute, "/", isPosixPathSeparator)

	if path == "" && !isAbsolute {
		path = "."
	}
	if path != "" && trailingSeparator {
		path += "/"
	}

	if isAbsolute {
		return "/" + path
	}
	return path
}

// IsAbsolute checks if a path is absolute on POSIX
func (p *PosixPath) IsAbsolute(path string) bool {
	if err := validateString(path, "path"); err != nil {
		panic(err)
	}
	return len(path) > 0 && path[0] == '/'
}

// Join joins path segments for POSIX
func (p *PosixPath) Join(paths ...string) string {
	if len(paths) == 0 {
		return "."
	}
	var joined string
	for _, path := range paths {
		if err := validateString(path, "path"); err != nil {
			panic(err)
		}
		if path != "" {
			if joined == "" {
				joined = path
			} else {
				joined += "/" + path
			}
		}
	}
	if joined == "" {
		return "."
	}
	return p.Normalize(joined)
}

// Relative calculates the relative path from 'from' to 'to' on POSIX
func (p *PosixPath) Relative(from, to string) string {
	if err := validateString(from, "from"); err != nil {
		panic(err)
	}
	if err := validateString(to, "to"); err != nil {
		panic(err)
	}

	if from == to {
		return ""
	}

	fromResolved := p.Resolve(from)
	toResolved := p.Resolve(to)

	if fromResolved == toResolved {
		return ""
	}

	fromSegs := strings.Split(fromResolved, "/")
	toSegs := strings.Split(toResolved, "/")

	var commonLength int
	for commonLength < len(fromSegs) && commonLength < len(toSegs) && fromSegs[commonLength] == toSegs[commonLength] {
		commonLength++
	}

	var pathSegs []string
	for i := commonLength; i < len(fromSegs); i++ {
		pathSegs = append(pathSegs, "..")
	}

	pathSegs = append(pathSegs, toSegs[commonLength:]...)

	return strings.Join(pathSegs, "/")
}

// Dirname returns the directory name of a path on POSIX
func (p *PosixPath) Dirname(path string) string {
	if err := validateString(path, "path"); err != nil {
		panic(err)
	}
	if len(path) == 0 {
		return "."
	}
	rootEnd := -1
	tail := -1
	for i := len(path) - 1; i >= 1; i-- {
		if path[i] == '/' {
			if tail == -1 {
				tail = i
				rootEnd = i
			}
			break
		}
	}
	if tail == -1 {
		if path[0] == '/' {
			return "/"
		}
		return "."
	}
	return path[:rootEnd]
}

// Basename returns the last portion of a path on POSIX
func (p *PosixPath) Basename(path string, suffix ...string) string {
	if err := validateString(path, "path"); err != nil {
		panic(err)
	}

	tail := -1
	for i := len(path) - 1; i >= 0; i-- {
		if path[i] == '/' {
			if tail == -1 {
				tail = i + 1
			}
			break
		}
	}

	if tail == -1 {
		tail = 0
	}

	base := path[tail:]
	if len(suffix) > 0 && suffix[0] != "" && strings.HasSuffix(base, suffix[0]) {
		return base[:len(base)-len(suffix[0])]
	}
	return base
}

// Extname returns the extension of a path on POSIX
func (p *PosixPath) Extname(path string) string {
	if err := validateString(path, "path"); err != nil {
		panic(err)
	}
	startDot := -1
	tail := -1
	for i := len(path) - 1; i >= 0; i-- {
		if path[i] == '/' {
			if tail == -1 {
				tail = i + 1
			}
			break
		}
		if startDot == -1 && path[i] == '.' {
			startDot = i
		}
	}
	if startDot == -1 || tail != -1 && startDot < tail {
		return ""
	}
	return path[startDot:]
}

// Format formats a path object into a POSIX path string
func (p *PosixPath) Format(pathObject ParsedPath) string {
	if err := validateObject(pathObject, "pathObject"); err != nil {
		panic(err)
	}
	return format("/", pathObject)
}

// Parse parses a POSIX path into its components
func (p *PosixPath) Parse(path string) ParsedPath {
	if err := validateString(path, "path"); err != nil {
		panic(err)
	}

	ret := ParsedPath{}
	if path == "" {
		return ret
	}

	isAbsolute := path[0] == '/'
	if isAbsolute {
		ret.Root = "/"
		ret.Dir = "/"
	}

	base := p.Basename(path)
	ret.Base = base
	ext := p.Extname(path)
	ret.Ext = ext
	if ext != "" {
		ret.Name = base[:len(base)-len(ext)]
	} else {
		ret.Name = base
	}

	if len(base) > 0 {
		dir := p.Dirname(path)
		if dir != "." {
			ret.Dir = dir
		} else if !isAbsolute {
			ret.Dir = ""
		}
	}

	return ret
}

// ToNamespacedPath returns the path as is for POSIX
func (p *PosixPath) ToNamespacedPath(path string) string {
	return path
}

// Sep returns the path separator for POSIX
func (p *PosixPath) Sep() string {
	return "/"
}

// Delimiter returns the path delimiter for POSIX
func (p *PosixPath) Delimiter() string {
	return ":"
}

// Global path instances
var (
	Win32 = &Win32Path{}
	Posix = &PosixPath{}
	// Exported instances matching TypeScript interface
	win32 = Win32
	posix = Posix
	sep   = "/"
)

// Default path functions based on platform
var (
	Normalize        func(string) string
	IsAbsolute       func(string) bool
	Join             func(...string) string
	Resolve          func(...string) string
	Relative         func(string, string) string
	Dirname          func(string) string
	Basename         func(string, ...string) string
	Extname          func(string) string
	Format           func(ParsedPath) string
	Parse            func(string) ParsedPath
	ToNamespacedPath func(string) string
	Sep              string
	Delimiter        string
)

func init() {
	if platformIsWin32 {
		Normalize = Win32.Normalize
		IsAbsolute = Win32.IsAbsolute
		Join = Win32.Join
		Resolve = Win32.Resolve
		Relative = Win32.Relative
		Dirname = Win32.Dirname
		Basename = Win32.Basename
		Extname = Win32.Extname
		Format = Win32.Format
		Parse = Win32.Parse
		ToNamespacedPath = Win32.ToNamespacedPath
		Sep = Win32.Sep()
		Delimiter = Win32.Delimiter()
		sep = Win32.Sep()
	} else {
		Normalize = Posix.Normalize
		IsAbsolute = Posix.IsAbsolute
		Join = Posix.Join
		Resolve = Posix.Resolve
		Relative = Posix.Relative
		Dirname = Posix.Dirname
		Basename = Posix.Basename
		Extname = Posix.Extname
		Format = Posix.Format
		Parse = Posix.Parse
		ToNamespacedPath = Posix.ToNamespacedPath
		Sep = Posix.Sep()
		Delimiter = Posix.Delimiter()
		sep = Posix.Sep()
	}
}

// Exported path separator constant
const SepChar = '/'
