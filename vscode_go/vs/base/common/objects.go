/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"encoding/json"
	"reflect"
)

// DeepClone performs a deep clone of any object using JSON marshaling/unmarshaling
func DeepClone[T any](obj T) T {
	var result T

	// Use JSON marshaling for deep clone
	data, err := json.Marshal(obj)
	if err != nil {
		return result
	}

	err = json.Unmarshal(data, &result)
	if err != nil {
		return result
	}

	return result
}

// DeepCloneMap performs a deep clone of a map
func DeepCloneMap[K comparable, V any](m map[K]V) map[K]V {
	if m == nil {
		return nil
	}

	result := make(map[K]V, len(m))
	for k, v := range m {
		result[k] = DeepClone(v)
	}
	return result
}

// DeepCloneSlice performs a deep clone of a slice
func DeepCloneSlice[T any](s []T) []T {
	if s == nil {
		return nil
	}

	result := make([]T, len(s))
	for i, v := range s {
		result[i] = DeepClone(v)
	}
	return result
}

// EqualsMap compares two maps for equality
func EqualsMap[K comparable, V any](a, b map[K]V, valueEqual func(V, V) bool) bool {
	if len(a) != len(b) {
		return false
	}

	if valueEqual == nil {
		valueEqual = func(v1, v2 V) bool {
			return Equals(v1, v2)
		}
	}

	for k, v1 := range a {
		v2, exists := b[k]
		if !exists || !valueEqual(v1, v2) {
			return false
		}
	}

	return true
}

// EqualsSlice compares two slices for equality
func EqualsSlice[T any](a, b []T, itemEqual func(T, T) bool) bool {
	if len(a) != len(b) {
		return false
	}

	if itemEqual == nil {
		itemEqual = func(t1, t2 T) bool {
			return Equals(t1, t2)
		}
	}

	for i := 0; i < len(a); i++ {
		if !itemEqual(a[i], b[i]) {
			return false
		}
	}

	return true
}

// Mixin copies properties from source to destination with optional overwrite control
func Mixin(destination, source map[string]interface{}, overwrite bool) map[string]interface{} {
	if destination == nil {
		destination = make(map[string]interface{})
	}

	if source == nil {
		return destination
	}

	for key, value := range source {
		if existing, exists := destination[key]; exists {
			if overwrite {
				if IsObject(existing) && IsObject(value) {
					destination[key] = Mixin(existing.(map[string]interface{}), value.(map[string]interface{}), overwrite)
				} else {
					destination[key] = value
				}
			}
		} else {
			destination[key] = value
		}
	}

	return destination
}

// DistinctObjects compares base and target objects and returns differences
func DistinctObjects(base, target map[string]interface{}) map[string]interface{} {
	result := make(map[string]interface{})

	if base == nil || target == nil {
		return result
	}

	for key, targetValue := range target {
		baseValue, exists := base[key]
		if !exists || !Equals(baseValue, targetValue) {
			result[key] = targetValue
		}
	}

	return result
}

// CloneAndChange clones an object and applies a changer function
func CloneAndChange(obj interface{}, changer func(interface{}) interface{}) interface{} {
	seen := make(map[uintptr]bool)
	return cloneAndChangeInternal(obj, changer, seen)
}

func cloneAndChangeInternal(obj interface{}, changer func(interface{}) interface{}, seen map[uintptr]bool) interface{} {
	if obj == nil {
		return nil
	}

	// Apply changer function first
	if changed := changer(obj); changed != nil {
		return changed
	}

	v := reflect.ValueOf(obj)
	switch v.Kind() {
	case reflect.Slice, reflect.Array:
		result := make([]interface{}, v.Len())
		for i := 0; i < v.Len(); i++ {
			result[i] = cloneAndChangeInternal(v.Index(i).Interface(), changer, seen)
		}
		return result

	case reflect.Map:
		// Check for cycles
		if v.Pointer() != 0 {
			if seen[v.Pointer()] {
				panic("Cannot clone recursive data-structure")
			}
			seen[v.Pointer()] = true
			defer delete(seen, v.Pointer())
		}

		result := make(map[string]interface{})
		for _, key := range v.MapKeys() {
			keyStr := key.String()
			value := v.MapIndex(key).Interface()
			result[keyStr] = cloneAndChangeInternal(value, changer, seen)
		}
		return result

	default:
		return obj
	}
}

// StructuralEquals performs structural equality comparison
func StructuralEquals(a, b interface{}) bool {
	if a == b {
		return true
	}

	va := reflect.ValueOf(a)
	vb := reflect.ValueOf(b)

	// Handle slices/arrays
	if va.Kind() == reflect.Slice && vb.Kind() == reflect.Slice {
		if va.Len() != vb.Len() {
			return false
		}
		for i := 0; i < va.Len(); i++ {
			if !StructuralEquals(va.Index(i).Interface(), vb.Index(i).Interface()) {
				return false
			}
		}
		return true
	}

	// Handle maps
	if va.Kind() == reflect.Map && vb.Kind() == reflect.Map {
		if va.Len() != vb.Len() {
			return false
		}

		for _, key := range va.MapKeys() {
			aVal := va.MapIndex(key)
			bVal := vb.MapIndex(key)

			if !bVal.IsValid() {
				return false
			}

			if !StructuralEquals(aVal.Interface(), bVal.Interface()) {
				return false
			}
		}
		return true
	}

	return reflect.DeepEqual(a, b)
}
