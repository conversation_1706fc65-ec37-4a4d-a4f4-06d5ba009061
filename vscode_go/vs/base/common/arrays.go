/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"errors"
	"math"
	"math/rand"
	"sort"
	"time"
)

// Shuffle uses <PERSON><PERSON><PERSON> shuffle to shuffle the given array
func Shuffle[T any](array []T, seed ...int) {
	var rng *rand.Rand

	if len(seed) > 0 {
		// Seeded random number generator
		rng = rand.New(rand.NewSource(int64(seed[0])))
	} else {
		// Use default random source
		rng = rand.New(rand.NewSource(time.Now().UnixNano()))
	}

	for i := len(array) - 1; i > 0; i-- {
		j := rng.Intn(i + 1)
		array[i], array[j] = array[j], array[i]
	}
}

// PushToStart pushes an element to the start of the array, if found
func PushToStart[T comparable](arr []T, value T) []T {
	for i, v := range arr {
		if v == value {
			// Remove element at index i
			arr = append(arr[:i], arr[i+1:]...)
			// Add to start
			return append([]T{value}, arr...)
		}
	}
	return arr
}

// PushToEnd pushes an element to the end of the array, if found
func PushToEnd[T comparable](arr []T, value T) []T {
	for i, v := range arr {
		if v == value {
			// Remove element at index i
			arr = append(arr[:i], arr[i+1:]...)
			// Add to end
			return append(arr, value)
		}
	}
	return arr
}

// PushMany pushes multiple items to an array
func PushMany[T any](arr []T, items []T) []T {
	return append(arr, items...)
}

// MapArrayOrNot applies a function to an item or array of items
func MapArrayOrNot[T, U any](items interface{}, fn func(T) U) interface{} {
	switch v := items.(type) {
	case []T:
		result := make([]U, len(v))
		for i, item := range v {
			result[i] = fn(item)
		}
		return result
	case T:
		return fn(v)
	default:
		panic("items must be T or []T")
	}
}

// AsArray converts a single item or array to an array
func AsArray[T any](x interface{}) []T {
	switch v := x.(type) {
	case []T:
		return v
	case T:
		return []T{v}
	default:
		panic("x must be T or []T")
	}
}

// GetRandomElement returns a random element from the array
func GetRandomElement[T any](arr []T) *T {
	if len(arr) == 0 {
		return nil
	}
	index := rand.Intn(len(arr))
	return &arr[index]
}

// ArrayContains checks if an array contains a specific element
func ArrayContains[T comparable](arr []T, element T) bool {
	for _, v := range arr {
		if v == element {
			return true
		}
	}
	return false
}

// Distinct returns a new array with unique elements
func Distinct[T comparable](arr []T) []T {
	seen := make(map[T]bool)
	result := make([]T, 0, len(arr))

	for _, v := range arr {
		if !seen[v] {
			seen[v] = true
			result = append(result, v)
		}
	}

	return result
}

// DistinctBy returns a new array with unique elements based on a key function
func DistinctBy[T any, K comparable](arr []T, keyFn func(T) K) []T {
	seen := make(map[K]bool)
	result := make([]T, 0, len(arr))

	for _, v := range arr {
		key := keyFn(v)
		if !seen[key] {
			seen[key] = true
			result = append(result, v)
		}
	}

	return result
}

// ArrayIndexOf returns the index of the first occurrence of element in array
func ArrayIndexOf[T comparable](arr []T, element T) int {
	for i, v := range arr {
		if v == element {
			return i
		}
	}
	return -1
}

// ArrayLastIndexOf returns the index of the last occurrence of element in array
func ArrayLastIndexOf[T comparable](arr []T, element T) int {
	for i := len(arr) - 1; i >= 0; i-- {
		if arr[i] == element {
			return i
		}
	}
	return -1
}

// Remove removes the first occurrence of element from array
func Remove[T comparable](arr []T, element T) []T {
	for i, v := range arr {
		if v == element {
			return append(arr[:i], arr[i+1:]...)
		}
	}
	return arr
}

// RemoveAll removes all occurrences of element from array
func RemoveAll[T comparable](arr []T, element T) []T {
	result := make([]T, 0, len(arr))
	for _, v := range arr {
		if v != element {
			result = append(result, v)
		}
	}
	return result
}

// Filter filters an array based on a predicate function
func Filter[T any](arr []T, predicate func(T) bool) []T {
	result := make([]T, 0, len(arr))
	for _, v := range arr {
		if predicate(v) {
			result = append(result, v)
		}
	}
	return result
}

// Map transforms an array using a mapping function
func Map[T, U any](arr []T, mapper func(T) U) []U {
	result := make([]U, len(arr))
	for i, v := range arr {
		result[i] = mapper(v)
	}
	return result
}

// Reduce reduces an array to a single value using a reducer function
func Reduce[T, U any](arr []T, reducer func(U, T) U, initial U) U {
	result := initial
	for _, v := range arr {
		result = reducer(result, v)
	}
	return result
}

// Find finds the first element that satisfies the predicate
func Find[T any](arr []T, predicate func(T) bool) *T {
	for _, v := range arr {
		if predicate(v) {
			return &v
		}
	}
	return nil
}

// FindIndex finds the index of the first element that satisfies the predicate
func FindIndex[T any](arr []T, predicate func(T) bool) int {
	for i, v := range arr {
		if predicate(v) {
			return i
		}
	}
	return -1
}

// Some checks if at least one element satisfies the predicate
func Some[T any](arr []T, predicate func(T) bool) bool {
	for _, v := range arr {
		if predicate(v) {
			return true
		}
	}
	return false
}

// Every checks if all elements satisfy the predicate
func Every[T any](arr []T, predicate func(T) bool) bool {
	for _, v := range arr {
		if !predicate(v) {
			return false
		}
	}
	return true
}

// ArrayReverse reverses an array in place
func ArrayReverse[T any](arr []T) {
	for i, j := 0, len(arr)-1; i < j; i, j = i+1, j-1 {
		arr[i], arr[j] = arr[j], arr[i]
	}
}

// Sort sorts an array using a comparison function. It's a wrapper around sort.Slice.
func Sort[T any](arr []T, compare func(T, T) int) {
	sort.Slice(arr, func(i, j int) bool {
		return compare(arr[i], arr[j]) < 0
	})
}

// Unique returns a new array with unique elements
func Unique[T comparable](arr []T) []T {
	seen := make(map[T]bool)
	result := make([]T, 0, len(arr))

	for _, v := range arr {
		if !seen[v] {
			seen[v] = true
			result = append(result, v)
		}
	}

	return result
}

// Flatten flattens a 2D array into a 1D array
func Flatten[T any](arr [][]T) []T {
	var result []T
	for _, subArr := range arr {
		result = append(result, subArr...)
	}
	return result
}

// Chunk splits an array into chunks of specified size
func Chunk[T any](arr []T, size int) [][]T {
	if size <= 0 {
		return nil
	}

	var result [][]T
	for i := 0; i < len(arr); i += size {
		end := i + size
		if end > len(arr) {
			end = len(arr)
		}
		result = append(result, arr[i:end])
	}

	return result
}

// Permutation represents a re-arrangement of items in an array
type Permutation struct {
	indexMap []int
}

// CreateSortPermutation returns a permutation that sorts the given array according to the given compare function
func CreateSortPermutation[T any](arr []T, compareFn func(T, T) int) *Permutation {
	indices := make([]int, len(arr))
	for i := range indices {
		indices[i] = i
	}

	// Sort indices based on the comparison of array elements
	Sort(indices, func(i, j int) int {
		return compareFn(arr[i], arr[j])
	})

	return &Permutation{indexMap: indices}
}

// ApplyPermutation returns a new array with the elements of the given array re-arranged according to this permutation
func ApplyPermutation[T any](p *Permutation, arr []T) []T {
	result := make([]T, len(arr))
	for i, index := range p.indexMap {
		if index < len(arr) {
			result[i] = arr[index]
		}
	}
	return result
}

// Min returns the minimum value in an array
func Min[T interface {
	~int | ~int8 | ~int16 | ~int32 | ~int64 | ~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64 | ~float32 | ~float64
}](arr []T) T {
	if len(arr) == 0 {
		var zero T
		return zero
	}

	min := arr[0]
	for _, v := range arr[1:] {
		if v < min {
			min = v
		}
	}
	return min
}

// Max returns the maximum value in an array
func Max[T interface {
	~int | ~int8 | ~int16 | ~int32 | ~int64 | ~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64 | ~float32 | ~float64
}](arr []T) T {
	if len(arr) == 0 {
		var zero T
		return zero
	}

	max := arr[0]
	for _, v := range arr[1:] {
		if v > max {
			max = v
		}
	}
	return max
}

// Sum returns the sum of all values in an array
func Sum[T interface {
	~int | ~int8 | ~int16 | ~int32 | ~int64 | ~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64 | ~float32 | ~float64
}](arr []T) T {
	var sum T
	for _, v := range arr {
		sum += v
	}
	return sum
}

// Average returns the average of all values in an array
func Average[T interface {
	~int | ~int8 | ~int16 | ~int32 | ~int64 | ~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64 | ~float32 | ~float64
}](arr []T) float64 {
	if len(arr) == 0 {
		return math.NaN()
	}

	sum := Sum(arr)
	return float64(sum) / float64(len(arr))
}

// Tail returns the last entry and the initial N-1 entries of the array.
func Tail[T any](arr []T) ([]T, T, error) {
	if len(arr) == 0 {
		var zero T
		return nil, zero, errors.New("Invalid tail call")
	}
	return arr[:len(arr)-1], arr[len(arr)-1], nil
}

// ArrayEquals checks if two arrays are equal.
func ArrayEquals[T any](one, other []T, itemEquals func(a, b T) bool) bool {
	if len(one) != len(other) {
		return false
	}
	for i := range one {
		if !itemEquals(one[i], other[i]) {
			return false
		}
	}
	return true
}

// RemoveFastWithoutKeepingOrder removes an element at `index` by replacing it with the last element.
func RemoveFastWithoutKeepingOrder[T any](array []T, index int) []T {
	last := len(array) - 1
	if index < last {
		array[index] = array[last]
	}
	return array[:last]
}

// BinarySearch performs a binary search on a sorted array.
func BinarySearch[T any](array []T, key T, comparator func(op1, op2 T) int) int {
	return BinarySearch2(len(array), func(i int) int {
		return comparator(array[i], key)
	})
}

// BinarySearch2 performs a binary search on a collection-like object.
func BinarySearch2(length int, compareToKey func(index int) int) int {
	low, high := 0, length-1
	for low <= high {
		mid := (low + high) / 2
		comp := compareToKey(mid)
		if comp < 0 {
			low = mid + 1
		} else if comp > 0 {
			high = mid - 1
		} else {
			return mid
		}
	}
	return -(low + 1)
}

// ArrayGroupBy groups elements of a sorted array.
func ArrayGroupBy[T any](data []T, compare func(a, b T) int) [][]T {
	result := [][]T{}
	var currentGroup []T
	// A copy is made to avoid modifying the original array.
	sortedData := make([]T, len(data))
	copy(sortedData, data)
	Sort(sortedData, compare)

	for _, element := range sortedData {
		if currentGroup == nil || compare(currentGroup[0], element) != 0 {
			currentGroup = []T{element}
			result = append(result, currentGroup)
		} else {
			currentGroup = append(currentGroup, element)
		}
	}
	return result
}

// CoalescePointerArray removes nil pointers from an array.
func CoalescePointerArray[T any](array []*T) []T {
	result := []T{}
	for _, e := range array {
		if e != nil {
			result = append(result, *e)
		}
	}
	return result
}

// CoalesceInPlace removes nil pointers from an array in-place.
func CoalesceInPlace[T any](array *[]*T) {
	to := 0
	for i := 0; i < len(*array); i++ {
		if (*array)[i] != nil {
			(*array)[to] = (*array)[i]
			to++
		}
	}
	*array = (*array)[:to]
}

// IsFalsyOrEmpty checks if an array is nil or empty.
func IsFalsyOrEmpty[T any](obj []T) bool {
	return obj == nil || len(obj) == 0
}

// IsNonEmptyArray checks if an array is not nil and not empty.
func IsNonEmptyArray[T any](obj []T) bool {
	return obj != nil && len(obj) > 0
}



// CoalesceString removes all empty strings from the array
func CoalesceString(array []string) []string {
	result := make([]string, 0, len(array))
	for _, v := range array {
		if v != "" {
			result = append(result, v)
		}
	}
	return result
}

// Coalesce removes all nil values from the array
func Coalesce[T any](array []*T) []T {
	result := make([]T, 0, len(array))
	for _, v := range array {
		if v != nil {
			result = append(result, *v)
		}
	}
	return result
}

// CoalesceInterface removes all nil values from interface array
func CoalesceInterface(array []interface{}) []interface{} {
	result := make([]interface{}, 0, len(array))
	for _, v := range array {
		if v != nil {
			result = append(result, v)
		}
	}
	return result
}
