/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"fmt"
	"regexp"
	"strings"
	"unicode"
)

// IsFalsyOrWhitespace checks if a string is falsy or contains only whitespace
func IsFalsyOrWhitespace(str string) bool {
	return strings.TrimSpace(str) == ""
}

// StringFormat helper to produce a string with a variable number of arguments
// Insert variable segments into the string using the {n} notation where N is the index of the argument
func StringFormat(value string, args ...interface{}) string {
	if len(args) == 0 {
		return value
	}

	re := regexp.MustCompile(`\{(\d+)\}`)
	return re.ReplaceAllStringFunc(value, func(match string) string {
		var idx int
		if _, err := fmt.Sscanf(match, "{%d}", &idx); err != nil {
			return match
		}
		if idx >= 0 && idx < len(args) {
			return fmt.Sprintf("%v", args[idx])
		}
		return match
	})
}

// Format2 helper to create a string from a template and a string record
func Format2(template string, values map[string]interface{}) string {
	if len(values) == 0 {
		return template
	}

	re := regexp.MustCompile(`\{([^}]+)\}`)
	return re.ReplaceAllStringFunc(template, func(match string) string {
		key := match[1 : len(match)-1] // Remove { and }
		if value, exists := values[key]; exists {
			return fmt.Sprintf("%v", value)
		}
		return match
	})
}

// EscapeRegExpCharacters escapes regular expression characters in a given string
func EscapeRegExpCharacters(value string) string {
	re := regexp.MustCompile(`[\\\{\}\*\+\?\|\^\$\.\[\]\(\)]`)
	return re.ReplaceAllStringFunc(value, func(match string) string {
		return "\\" + match
	})
}

// Count counts how often substr occurs inside value
func Count(value, substr string) int {
	if substr == "" {
		return 0
	}

	count := 0
	index := 0
	for {
		pos := strings.Index(value[index:], substr)
		if pos == -1 {
			break
		}
		count++
		index += pos + len(substr)
	}
	return count
}

// Compare compares two strings lexicographically
func Compare(a, b string) int {
	if a < b {
		return -1
	} else if a > b {
		return 1
	}
	return 0
}

// CompareSubstring compares substrings of two strings
func CompareSubstring(a, b string, aStart, aEnd, bStart, bEnd int) int {
	// Validate bounds
	if aStart < 0 {
		aStart = 0
	}
	if aEnd > len(a) {
		aEnd = len(a)
	}
	if bStart < 0 {
		bStart = 0
	}
	if bEnd > len(b) {
		bEnd = len(b)
	}

	for aStart < aEnd && bStart < bEnd {
		codeA := a[aStart]
		codeB := b[bStart]
		if codeA < codeB {
			return -1
		} else if codeA > codeB {
			return 1
		}
		aStart++
		bStart++
	}

	aLen := aEnd - aStart
	bLen := bEnd - bStart

	if aLen < bLen {
		return -1
	} else if aLen > bLen {
		return 1
	}
	return 0
}

// CompareIgnoreCase compares two strings ignoring case
func CompareIgnoreCase(a, b string) int {
	return CompareSubstringIgnoreCase(a, b, 0, len(a), 0, len(b))
}

// isLowerAsciiLetter checks if a byte represents a lowercase ASCII letter
func isLowerAsciiLetter(code byte) bool {
	return code >= CharCodeLowerA && code <= CharCodeLowerZ
}

// CompareSubstringIgnoreCase compares substrings of two strings ignoring case
func CompareSubstringIgnoreCase(a, b string, aStart, aEnd, bStart, bEnd int) int {
	// Validate bounds
	if aStart < 0 {
		aStart = 0
	}
	if aEnd > len(a) {
		aEnd = len(a)
	}
	if bStart < 0 {
		bStart = 0
	}
	if bEnd > len(b) {
		bEnd = len(b)
	}

	for aStart < aEnd && bStart < bEnd {
		codeA := a[aStart]
		codeB := b[bStart]

		if codeA == codeB {
			// equal
			aStart++
			bStart++
			continue
		}

		if codeA >= 128 || codeB >= 128 {
			// not ASCII letters -> fallback to lower-casing strings
			return CompareSubstring(strings.ToLower(a), strings.ToLower(b), aStart, aEnd, bStart, bEnd)
		}

		// map lower-case ascii letter onto upper-case variants
		// [97-122] (lower ascii) --> [65-90] (upper ascii)
		if isLowerAsciiLetter(codeA) {
			codeA -= 32
		}
		if isLowerAsciiLetter(codeB) {
			codeB -= 32
		}

		// compare both code points
		diff := int(codeA) - int(codeB)
		if diff == 0 {
			aStart++
			bStart++
			continue
		}

		return diff
	}

	aLen := aEnd - aStart
	bLen := bEnd - bStart

	if aLen < bLen {
		return -1
	} else if aLen > bLen {
		return 1
	}
	return 0
}

// StartsWith checks if a string starts with a prefix
func StartsWith(str, prefix string) bool {
	return strings.HasPrefix(str, prefix)
}

// StartsWithIgnoreCase checks if a string starts with a prefix (case insensitive)
func StartsWithIgnoreCase(str, prefix string) bool {
	return strings.HasPrefix(strings.ToLower(str), strings.ToLower(prefix))
}

// startsWithIgnoreCase is the lowercase alias for compatibility
var startsWithIgnoreCase = StartsWithIgnoreCase

// EndsWith checks if a string ends with a suffix
func EndsWith(str, suffix string) bool {
	return strings.HasSuffix(str, suffix)
}

// EndsWithIgnoreCase checks if a string ends with a suffix (case insensitive)
func EndsWithIgnoreCase(str, suffix string) bool {
	return strings.HasSuffix(strings.ToLower(str), strings.ToLower(suffix))
}

// EqualsIgnoreCase compares two strings ignoring case
func EqualsIgnoreCase(a, b string) bool {
	return strings.EqualFold(a, b)
}

// CommonPrefixLength returns the length of the common prefix of two strings
func CommonPrefixLength(a, b string) int {
	i := 0
	minLen := len(a)
	if len(b) < minLen {
		minLen = len(b)
	}

	for i < minLen && a[i] == b[i] {
		i++
	}
	return i
}

// CommonSuffixLength returns the length of the common suffix of two strings
func CommonSuffixLength(a, b string) int {
	i := 0
	aLen := len(a)
	bLen := len(b)
	minLen := aLen
	if bLen < minLen {
		minLen = bLen
	}

	for i < minLen && a[aLen-1-i] == b[bLen-1-i] {
		i++
	}
	return i
}

// IsUpperAsciiLetter checks if a character is an uppercase ASCII letter
func IsUpperAsciiLetter(ch byte) bool {
	return ch >= CharCodeA && ch <= CharCodeZ
}

// IsLowerAsciiLetter checks if a character is a lowercase ASCII letter
func IsLowerAsciiLetter(ch byte) bool {
	return ch >= CharCodeLowerA && ch <= CharCodeLowerZ
}

// IsAsciiLetter checks if a character is an ASCII letter
func IsAsciiLetter(ch byte) bool {
	return IsUpperAsciiLetter(ch) || IsLowerAsciiLetter(ch)
}

// IsAsciiDigit checks if a character is an ASCII digit
func IsAsciiDigit(ch byte) bool {
	return ch >= CharCode0 && ch <= CharCode9
}

// Trim trims whitespace from both ends of a string
func Trim(str string) string {
	return strings.TrimSpace(str)
}

// TrimLeft trims whitespace from the left end of a string
func TrimLeft(str string) string {
	return strings.TrimLeftFunc(str, unicode.IsSpace)
}

// TrimRight trims whitespace from the right end of a string
func TrimRight(str string) string {
	return strings.TrimRightFunc(str, unicode.IsSpace)
}

// rtrim removes all occurrences of needle from the end of haystack
func rtrim(haystack, needle string) string {
	if haystack == "" || needle == "" {
		return haystack
	}

	needleLen := len(needle)
	haystackLen := len(haystack)

	if needleLen == 0 || haystackLen == 0 {
		return haystack
	}

	offset := haystackLen
	for {
		idx := strings.LastIndex(haystack[:offset], needle)
		if idx == -1 || idx+needleLen != offset {
			break
		}
		if idx == 0 {
			return ""
		}
		offset = idx
	}

	return haystack[:offset]
}

// Rtrim removes all occurrences of needle from the end of haystack (exported version)
func Rtrim(haystack, needle string) string {
	return rtrim(haystack, needle)
}

// Reverse reverses a string
func Reverse(str string) string {
	runes := []rune(str)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}

// Repeat repeats a string n times
func Repeat(str string, count int) string {
	if count <= 0 {
		return ""
	}
	return strings.Repeat(str, count)
}

// PadLeft pads a string to a certain length with spaces on the left
func PadLeft(str string, length int) string {
	if len(str) >= length {
		return str
	}
	return strings.Repeat(" ", length-len(str)) + str
}

// PadRight pads a string to a certain length with spaces on the right
func PadRight(str string, length int) string {
	if len(str) >= length {
		return str
	}
	return str + strings.Repeat(" ", length-len(str))
}

// PadLeftWith pads a string to a certain length with a specific character on the left
func PadLeftWith(str string, length int, padChar string) string {
	if len(str) >= length {
		return str
	}
	return strings.Repeat(padChar, length-len(str)) + str
}

// PadRightWith pads a string to a certain length with a specific character on the right
func PadRightWith(str string, length int, padChar string) string {
	if len(str) >= length {
		return str
	}
	return str + strings.Repeat(padChar, length-len(str))
}

// Contains checks if a string contains a substring
func Contains(str, substr string) bool {
	return strings.Contains(str, substr)
}

// ContainsIgnoreCase checks if a string contains a substring (case insensitive)
func ContainsIgnoreCase(str, substr string) bool {
	return strings.Contains(strings.ToLower(str), strings.ToLower(substr))
}

// IndexOf returns the index of the first occurrence of substr in str
func IndexOf(str, substr string) int {
	return strings.Index(str, substr)
}

// IndexOfIgnoreCase returns the index of the first occurrence of substr in str (case insensitive)
func IndexOfIgnoreCase(str, substr string) int {
	return strings.Index(strings.ToLower(str), strings.ToLower(substr))
}

// LastIndexOf returns the index of the last occurrence of substr in str
func LastIndexOf(str, substr string) int {
	return strings.LastIndex(str, substr)
}

// LastIndexOfIgnoreCase returns the index of the last occurrence of substr in str (case insensitive)
func LastIndexOfIgnoreCase(str, substr string) int {
	return strings.LastIndex(strings.ToLower(str), strings.ToLower(substr))
}

// Split splits a string by a separator
func Split(str, sep string) []string {
	if str == "" {
		return []string{}
	}
	return strings.Split(str, sep)
}

// StringJoin joins strings with a separator
func StringJoin(strs []string, sep string) string {
	return strings.Join(strs, sep)
}

// Replace replaces all occurrences of old with new in str
func Replace(str, old, new string) string {
	return strings.ReplaceAll(str, old, new)
}

// ReplaceFirst replaces the first occurrence of old with new in str
func ReplaceFirst(str, old, new string) string {
	return strings.Replace(str, old, new, 1)
}

// StringToUpperCase converts a string to uppercase
func StringToUpperCase(str string) string {
	return strings.ToUpper(str)
}

// StringToLowerCase converts a string to lowercase
func StringToLowerCase(str string) string {
	return strings.ToLower(str)
}

// Capitalize capitalizes the first letter of a string
func Capitalize(str string) string {
	if len(str) == 0 {
		return str
	}
	return strings.ToUpper(str[:1]) + str[1:]
}

// Uncapitalize uncapitalizes the first letter of a string
func Uncapitalize(str string) string {
	if len(str) == 0 {
		return str
	}
	return strings.ToLower(str[:1]) + str[1:]
}
