/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package electronmain

import (
	"fmt"
	"net/url"
	"strings"
	"sync"

	"github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
)

// Note: This is a mock implementation. The actual IPC mechanism would be different in a pure Go app.
// We are replicating the API for migration purposes.

// IpcMainEvent mimics electron.IpcMainEvent
type IpcMainEvent struct {
	SenderFrame *Frame
}

// IpcMainInvokeEvent mimics electron.IpcMainInvokeEvent
type IpcMainInvokeEvent struct {
	SenderFrame *Frame
}

// Frame mimics electron.WebFrameMain
type Frame struct {
	URL    string
	Parent *Frame
}

type ipcMainListener func(event IpcMainEvent, args ...interface{})
type ipcMainInvokeHandler func(event IpcMainInvokeEvent, args ...interface{}) (interface{}, error)

// Mock ipcMain for demonstration
var mockIPC = &mockIpcMain{
	listeners: make(map[string][]ipcMainListener),
	handlers:  make(map[string]ipcMainInvokeHandler),
}

type mockIpcMain struct {
	listeners map[string][]ipcMainListener
	handlers  map[string]ipcMainInvokeHandler
	mu        sync.Mutex
}

func (m *mockIpcMain) On(channel string, listener ipcMainListener) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.listeners[channel] = append(m.listeners[channel], listener)
}

func (m *mockIpcMain) Once(channel string, listener ipcMainListener) {
	// Simplified: not implementing once logic for this mock
	m.On(channel, listener)
}

func (m *mockIpcMain) Handle(channel string, handler ipcMainInvokeHandler) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.handlers[channel] = handler
}

func (m *mockIpcMain) RemoveHandler(channel string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	delete(m.handlers, channel)
}

func (m *mockIpcMain) RemoveListener(channel string, listener ipcMainListener) {
	// Simplified: not implementing remove listener for this mock
}

// ValidatedIpcMain is a drop-in replacement for electron.ipcMain that validates the sender.
type ValidatedIpcMain struct {
	mapListenerToWrapper sync.Map // map[ipcMainListener]ipcMainListener
}

// On listens to a channel.
func (v *ValidatedIpcMain) On(channel string, listener ipcMainListener) *ValidatedIpcMain {
	wrappedListener := func(event IpcMainEvent, args ...interface{}) {
		if v.validateEvent(channel, event.SenderFrame) {
			listener(event, args...)
		}
	}
	v.mapListenerToWrapper.Store(listener, wrappedListener)
	mockIPC.On(channel, wrappedListener)
	return v
}

// Once listens to a channel for a single event.
func (v *ValidatedIpcMain) Once(channel string, listener ipcMainListener) *ValidatedIpcMain {
	// The original listener is not stored for `once` in the TS version, so we don't store it here either.
	mockIPC.Once(channel, func(event IpcMainEvent, args ...interface{}) {
		if v.validateEvent(channel, event.SenderFrame) {
			listener(event, args...)
		}
	})
	return v
}

// Handle adds a handler for an invokable IPC.
func (v *ValidatedIpcMain) Handle(channel string, listener func(event IpcMainInvokeEvent, args ...interface{}) (interface{}, error)) *ValidatedIpcMain {
	mockIPC.Handle(channel, func(event IpcMainInvokeEvent, args ...interface{}) (interface{}, error) {
		if v.validateEvent(channel, event.SenderFrame) {
			return listener(event, args...)
		}
		return nil, fmt.Errorf("Invalid channel '%s' or sender for ipcMain.handle() usage", channel)
	})
	return v
}

// RemoveHandler removes a handler for a channel.
func (v *ValidatedIpcMain) RemoveHandler(channel string) *ValidatedIpcMain {
	mockIPC.RemoveHandler(channel)
	return v
}

// RemoveListener removes a listener for a channel.
func (v *ValidatedIpcMain) RemoveListener(channel string, listener ipcMainListener) *ValidatedIpcMain {
	if wrapped, ok := v.mapListenerToWrapper.Load(listener); ok {
		if wrappedListener, ok := wrapped.(ipcMainListener); ok {
			mockIPC.RemoveListener(channel, wrappedListener)
			v.mapListenerToWrapper.Delete(listener)
		}
	}
	return v
}

func (v *ValidatedIpcMain) validateEvent(channel string, sender *Frame) bool {
	if !strings.HasPrefix(channel, "vscode:") {
		common.OnUnexpectedError(fmt.Errorf("Refused to handle ipcMain event for channel '%s' because the channel is unknown", channel))
		return false
	}

	if sender == nil {
		// This can happen in tests or specific scenarios.
		return true
	}

	senderURL := sender.URL
	if senderURL == "" || senderURL == "about:blank" {
		return true
	}

	u, err := url.Parse(senderURL)
	if err != nil {
		common.OnUnexpectedError(fmt.Errorf("Refused to handle ipcMain event for channel '%s' because of a malformed URL '%s'", channel, senderURL))
		return false
	}

	if u.Host != common.VSCODE_AUTHORITY {
		common.OnUnexpectedError(fmt.Errorf("Refused to handle ipcMain event for channel '%s' because of a bad origin of '%s'", channel, u.Host))
		return false
	}

	if sender.Parent != nil {
		common.OnUnexpectedError(fmt.Errorf("Refused to handle ipcMain event for channel '%s' because sender of origin '%s' is not a main frame", channel, u.Host))
		return false
	}

	return true
}

// ValidatedIpcMain is the global instance.
var IpcMain = &ValidatedIpcMain{}
