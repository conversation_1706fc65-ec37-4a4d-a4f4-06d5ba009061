/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	files "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	instantiation "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
)

// IUriIdentityService provides URI identity and comparison services
type IUriIdentityService interface {
	// Service brand for type safety
	ServiceBrand() interface{}

	// ExtUri provides URI extensions that are aware of casing
	ExtUri() basecommon.IExtUri

	// AsCanonicalUri returns a canonical uri for the given resource
	AsCanonicalUri(uri *basecommon.URI) *basecommon.URI
}

// UriIdentityService implements IUriIdentityService
type UriIdentityService struct {
	fileService   files.IFileService
	extUri        basecommon.IExtUri
	canonicalUris *basecommon.LRUCache[*basecommon.URI, *basecommon.URI]
}

// NewUriIdentityService creates a new URI identity service
func NewUriIdentityService(fileService files.IFileService) *UriIdentityService {
	schemeIgnoresPathCasingCache := make(map[string]bool)

	ignorePathCasing := func(uri *basecommon.URI) bool {
		val, ok := schemeIgnoresPathCasingCache[uri.Scheme]
		if !ok {
			val = fileService.HasProvider(uri) && !fileService.HasCapability(uri, files.FileSystemProviderCapabilitiesPathCaseSensitive)
			schemeIgnoresPathCasingCache[uri.Scheme] = val
		}
		return val
	}

	// TODO: Listen for file system provider changes to clear the cache

	return &UriIdentityService{
		fileService:   fileService,
		extUri:        basecommon.NewExtUri(ignorePathCasing),
		canonicalUris: basecommon.NewLRUCache[*basecommon.URI, *basecommon.URI](1024),
	}
}

// ServiceBrand implements the service brand
func (uis *UriIdentityService) ServiceBrand() interface{} {
	return "uriIdentityService"
}

// ExtUri returns the extended URI operations
func (uis *UriIdentityService) ExtUri() basecommon.IExtUri {
	return uis.extUri
}

// AsCanonicalUri returns a canonical URI
func (uis *UriIdentityService) AsCanonicalUri(uri *basecommon.URI) *basecommon.URI {
	if uis.fileService.HasProvider(uri) {
		uri = uis.extUri.NormalizePath(uri)
	}

	canonical, ok := uis.canonicalUris.Get(uri)
	if ok {
		return canonical.With(basecommon.UriComponents{Fragment: uri.Fragment})
	}

	uis.canonicalUris.Set(uri, uri)

	return uri
}

// Service identifier
var IUriIdentityServiceID = instantiation.CreateDecorator[IUriIdentityService]("uriIdentityService")
