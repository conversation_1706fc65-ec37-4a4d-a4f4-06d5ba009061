/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"regexp"
	"sync"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	lifecyclecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	nlscommon "github.com/yudaprama/kawai-agent/vscode_go/vs/nls"
	jsonschemacommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/jsonschemas/common"
	registrycommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/registry/common"
)

// IConfigurationDefaultOverride represents a configuration default override
type IConfigurationDefaultOverride struct {
	Value  interface{}     `json:"value"`
	Source *IExtensionInfo `json:"source,omitempty"`
}

// EditPresentationTypes represents the presentation types for editing
type EditPresentationTypes string

const (
	EditPresentationTypesMultiline  EditPresentationTypes = "multilineText"
	EditPresentationTypesSingleline EditPresentationTypes = "singlelineText"
)

// Global settings objects
var (
	AllSettings                = &SettingsGroup{Properties: make(basecommon.IStringDictionary[*IConfigurationPropertySchema]), PatternProperties: make(basecommon.IStringDictionary[*IConfigurationPropertySchema])}
	ApplicationSettings        = &SettingsGroup{Properties: make(basecommon.IStringDictionary[*IConfigurationPropertySchema]), PatternProperties: make(basecommon.IStringDictionary[*IConfigurationPropertySchema])}
	ApplicationMachineSettings = &SettingsGroup{Properties: make(basecommon.IStringDictionary[*IConfigurationPropertySchema]), PatternProperties: make(basecommon.IStringDictionary[*IConfigurationPropertySchema])}
	MachineSettings            = &SettingsGroup{Properties: make(basecommon.IStringDictionary[*IConfigurationPropertySchema]), PatternProperties: make(basecommon.IStringDictionary[*IConfigurationPropertySchema])}
	MachineOverridableSettings = &SettingsGroup{Properties: make(basecommon.IStringDictionary[*IConfigurationPropertySchema]), PatternProperties: make(basecommon.IStringDictionary[*IConfigurationPropertySchema])}
	WindowSettings             = &SettingsGroup{Properties: make(basecommon.IStringDictionary[*IConfigurationPropertySchema]), PatternProperties: make(basecommon.IStringDictionary[*IConfigurationPropertySchema])}
	ResourceSettings           = &SettingsGroup{Properties: make(basecommon.IStringDictionary[*IConfigurationPropertySchema]), PatternProperties: make(basecommon.IStringDictionary[*IConfigurationPropertySchema])}
)

// SettingsGroup represents a group of settings
type SettingsGroup struct {
	Properties        basecommon.IStringDictionary[*IConfigurationPropertySchema] `json:"properties"`
	PatternProperties basecommon.IStringDictionary[*IConfigurationPropertySchema] `json:"patternProperties"`
}

// Schema IDs
const (
	ResourceLanguageSettingsSchemaID = "vscode://schemas/settings/resourceLanguage"
	ConfigurationDefaultsSchemaID    = "vscode://schemas/settings/configurationDefaults"
)

// Regular expressions for override properties
var (
	OverrideIdentifierPattern = `\[([^\]]+)\]`
	OverrideIdentifierRegex   = regexp.MustCompile(OverrideIdentifierPattern)
	OverridePropertyPattern   = `^(` + OverrideIdentifierPattern + `)+$`
	OverridePropertyRegex     = regexp.MustCompile(OverridePropertyPattern)
)

// ConfigurationRegistry implements IConfigurationRegistry
type ConfigurationRegistry struct {
	*lifecyclecommon.Disposable

	registeredConfigurationDefaults []*IConfigurationDefaults
	configurationDefaultsOverrides  map[string]*struct {
		ConfigurationDefaultOverrides     []*IConfigurationDefaultOverride
		ConfigurationDefaultOverrideValue *IConfigurationDefaultOverrideValue
	}
	defaultLanguageConfigurationOverridesNode *IConfigurationNode
	configurationContributors                 []*IConfigurationNode
	configurationProperties                   basecommon.IStringDictionary[*IRegisteredConfigurationPropertySchema]
	policyConfigurations                      map[string]string
	excludedConfigurationProperties           basecommon.IStringDictionary[*IRegisteredConfigurationPropertySchema]
	resourceLanguageSettingsSchema            *basecommon.IJSONSchema
	overrideIdentifiers                       map[string]bool

	onDidSchemaChangeEmitter        *basecommon.Emitter[interface{}]
	onDidUpdateConfigurationEmitter *basecommon.Emitter[*IConfigurationUpdateEvent]

	mu sync.RWMutex
}

// NewConfigurationRegistry creates a new configuration registry
func NewConfigurationRegistry() *ConfigurationRegistry {
	registry := &ConfigurationRegistry{
		Disposable:                      lifecyclecommon.NewDisposable(),
		registeredConfigurationDefaults: make([]*IConfigurationDefaults, 0),
		configurationDefaultsOverrides: make(map[string]*struct {
			ConfigurationDefaultOverrides     []*IConfigurationDefaultOverride
			ConfigurationDefaultOverrideValue *IConfigurationDefaultOverrideValue
		}),
		configurationContributors:       make([]*IConfigurationNode, 0),
		configurationProperties:         make(basecommon.IStringDictionary[*IRegisteredConfigurationPropertySchema]),
		policyConfigurations:            make(map[string]string),
		excludedConfigurationProperties: make(basecommon.IStringDictionary[*IRegisteredConfigurationPropertySchema]),
		overrideIdentifiers:             make(map[string]bool),
		onDidSchemaChangeEmitter:        basecommon.NewEmitter[interface{}](),
		onDidUpdateConfigurationEmitter: basecommon.NewEmitter[*IConfigurationUpdateEvent](),
	}

	// Initialize default language configuration overrides node
	registry.defaultLanguageConfigurationOverridesNode = &IConfigurationNode{
		ID:         stringPtr("defaultOverrides"),
		Title:      stringPtr(nlscommon.Localize("defaultLanguageConfigurationOverrides.title", "Default Language Configuration Overrides")),
		Properties: make(basecommon.IStringDictionary[*IConfigurationPropertySchema]),
	}

	registry.configurationContributors = append(registry.configurationContributors, registry.defaultLanguageConfigurationOverridesNode)

	// Initialize resource language settings schema
	registry.resourceLanguageSettingsSchema = &basecommon.IJSONSchema{
		Properties:           make(basecommon.IJSONSchemaMap),
		PatternProperties:    make(basecommon.IJSONSchemaMap),
		AdditionalProperties: true,
		AllowTrailingCommas:  boolPtr(true),
		AllowComments:        boolPtr(true),
	}

	// Register the schema if the contribution registry is available
	if contributionRegistry := registrycommon.Registry.As(jsonschemacommon.Extensions.JSONContribution); contributionRegistry != nil {
		if jsonRegistry, ok := contributionRegistry.(jsonschemacommon.IJSONContributionRegistry); ok {
			jsonRegistry.RegisterSchema(ResourceLanguageSettingsSchemaID, registry.resourceLanguageSettingsSchema, nil)
		}
	}

	registry.registerOverridePropertyPatternKey()

	return registry
}

// RegisterConfiguration registers a single configuration
func (r *ConfigurationRegistry) RegisterConfiguration(configuration *IConfigurationNode) *IConfigurationNode {
	r.RegisterConfigurations([]*IConfigurationNode{configuration}, true)
	return configuration
}

// RegisterConfigurations registers multiple configurations
func (r *ConfigurationRegistry) RegisterConfigurations(configurations []*IConfigurationNode, validate bool) {
	r.mu.Lock()
	defer r.mu.Unlock()

	properties := make(map[string]bool)
	r.doRegisterConfigurations(configurations, validate, properties)

	// Register schema and fire events
	if contributionRegistry := registrycommon.Registry.As(jsonschemacommon.Extensions.JSONContribution); contributionRegistry != nil {
		if jsonRegistry, ok := contributionRegistry.(jsonschemacommon.IJSONContributionRegistry); ok {
			jsonRegistry.RegisterSchema(ResourceLanguageSettingsSchemaID, r.resourceLanguageSettingsSchema, nil)
		}
	}

	r.onDidSchemaChangeEmitter.Fire(nil)
	r.onDidUpdateConfigurationEmitter.Fire(&IConfigurationUpdateEvent{
		Properties: r.createSetFromMap(properties),
	})
}

// DeregisterConfigurations deregisters multiple configurations
func (r *ConfigurationRegistry) DeregisterConfigurations(configurations []*IConfigurationNode) {
	r.mu.Lock()
	defer r.mu.Unlock()

	properties := make(map[string]bool)
	r.doDeregisterConfigurations(configurations, properties)

	if contributionRegistry := registrycommon.Registry.As(jsonschemacommon.Extensions.JSONContribution); contributionRegistry != nil {
		if jsonRegistry, ok := contributionRegistry.(jsonschemacommon.IJSONContributionRegistry); ok {
			jsonRegistry.RegisterSchema(ResourceLanguageSettingsSchemaID, r.resourceLanguageSettingsSchema, nil)
		}
	}

	r.onDidSchemaChangeEmitter.Fire(nil)
	r.onDidUpdateConfigurationEmitter.Fire(&IConfigurationUpdateEvent{
		Properties: r.createSetFromMap(properties),
	})
}

// UpdateConfigurations updates configurations by adding and removing
func (r *ConfigurationRegistry) UpdateConfigurations(configurations *IConfigurationRegistryUpdate) {
	r.mu.Lock()
	defer r.mu.Unlock()

	properties := make(map[string]bool)
	r.doDeregisterConfigurations(configurations.Remove, properties)
	r.doRegisterConfigurations(configurations.Add, false, properties)

	if contributionRegistry := registrycommon.Registry.As(jsonschemacommon.Extensions.JSONContribution); contributionRegistry != nil {
		if jsonRegistry, ok := contributionRegistry.(jsonschemacommon.IJSONContributionRegistry); ok {
			jsonRegistry.RegisterSchema(ResourceLanguageSettingsSchemaID, r.resourceLanguageSettingsSchema, nil)
		}
	}

	r.onDidSchemaChangeEmitter.Fire(nil)
	r.onDidUpdateConfigurationEmitter.Fire(&IConfigurationUpdateEvent{
		Properties: r.createSetFromMap(properties),
	})
}

// OnDidSchemaChange returns the schema change event
func (r *ConfigurationRegistry) OnDidSchemaChange() basecommon.Event[interface{}] {
	return r.onDidSchemaChangeEmitter.Event()
}

// OnDidUpdateConfiguration returns the configuration update event
func (r *ConfigurationRegistry) OnDidUpdateConfiguration() basecommon.Event[*IConfigurationUpdateEvent] {
	return r.onDidUpdateConfigurationEmitter.Event()
}

// GetConfigurations returns all configuration nodes
func (r *ConfigurationRegistry) GetConfigurations() []*IConfigurationNode {
	r.mu.RLock()
	defer r.mu.RUnlock()

	result := make([]*IConfigurationNode, len(r.configurationContributors))
	copy(result, r.configurationContributors)
	return result
}

// GetConfigurationProperties returns all configuration properties
func (r *ConfigurationRegistry) GetConfigurationProperties() basecommon.IStringDictionary[*IRegisteredConfigurationPropertySchema] {
	r.mu.RLock()
	defer r.mu.RUnlock()

	result := make(basecommon.IStringDictionary[*IRegisteredConfigurationPropertySchema])
	for k, v := range r.configurationProperties {
		result[k] = v
	}
	return result
}

// GetPolicyConfigurations returns all policy configurations
func (r *ConfigurationRegistry) GetPolicyConfigurations() map[string]string {
	r.mu.RLock()
	defer r.mu.RUnlock()

	result := make(map[string]string)
	for k, v := range r.policyConfigurations {
		result[k] = v
	}
	return result
}

// GetExcludedConfigurationProperties returns all excluded configuration properties
func (r *ConfigurationRegistry) GetExcludedConfigurationProperties() basecommon.IStringDictionary[*IRegisteredConfigurationPropertySchema] {
	r.mu.RLock()
	defer r.mu.RUnlock()

	result := make(basecommon.IStringDictionary[*IRegisteredConfigurationPropertySchema])
	for k, v := range r.excludedConfigurationProperties {
		result[k] = v
	}
	return result
}

// RegisterDefaultConfigurations registers default configurations
func (r *ConfigurationRegistry) RegisterDefaultConfigurations(defaultConfigurations []*IConfigurationDefaults) {
	r.mu.Lock()
	defer r.mu.Unlock()

	properties := make(map[string]bool)
	r.doRegisterDefaultConfigurations(defaultConfigurations, properties)

	r.onDidSchemaChangeEmitter.Fire(nil)
	r.onDidUpdateConfigurationEmitter.Fire(&IConfigurationUpdateEvent{
		Properties:        r.createSetFromMap(properties),
		DefaultsOverrides: boolPtr(true),
	})
}

// DeregisterDefaultConfigurations deregisters default configurations
func (r *ConfigurationRegistry) DeregisterDefaultConfigurations(defaultConfigurations []*IConfigurationDefaults) {
	r.mu.Lock()
	defer r.mu.Unlock()

	properties := make(map[string]bool)
	r.doDeregisterDefaultConfigurations(defaultConfigurations, properties)

	r.onDidSchemaChangeEmitter.Fire(nil)
	r.onDidUpdateConfigurationEmitter.Fire(&IConfigurationUpdateEvent{
		Properties:        r.createSetFromMap(properties),
		DefaultsOverrides: boolPtr(true),
	})
}

// DeltaConfiguration performs bulk configuration updates
func (r *ConfigurationRegistry) DeltaConfiguration(delta *IConfigurationDelta) {
	r.mu.Lock()
	defer r.mu.Unlock()

	properties := make(map[string]bool)
	defaultsOverrides := false

	// Remove defaults
	if delta.RemovedDefaults != nil {
		r.doDeregisterDefaultConfigurations(delta.RemovedDefaults, properties)
		defaultsOverrides = true
	}

	// Add defaults
	if delta.AddedDefaults != nil {
		r.doRegisterDefaultConfigurations(delta.AddedDefaults, properties)
		defaultsOverrides = true
	}

	// Remove configurations
	if delta.RemovedConfigurations != nil {
		r.doDeregisterConfigurations(delta.RemovedConfigurations, properties)
	}

	// Add configurations
	if delta.AddedConfigurations != nil {
		r.doRegisterConfigurations(delta.AddedConfigurations, false, properties)
	}

	r.onDidSchemaChangeEmitter.Fire(nil)
	r.onDidUpdateConfigurationEmitter.Fire(&IConfigurationUpdateEvent{
		Properties:        r.createSetFromMap(properties),
		DefaultsOverrides: &defaultsOverrides,
	})
}

// GetRegisteredDefaultConfigurations returns all registered default configurations
func (r *ConfigurationRegistry) GetRegisteredDefaultConfigurations() []*IConfigurationDefaults {
	r.mu.RLock()
	defer r.mu.RUnlock()

	result := make([]*IConfigurationDefaults, len(r.registeredConfigurationDefaults))
	copy(result, r.registeredConfigurationDefaults)
	return result
}

// GetConfigurationDefaultsOverrides returns configuration defaults overrides
func (r *ConfigurationRegistry) GetConfigurationDefaultsOverrides() map[string]*IConfigurationDefaultOverrideValue {
	r.mu.RLock()
	defer r.mu.RUnlock()

	result := make(map[string]*IConfigurationDefaultOverrideValue)
	for key, value := range r.configurationDefaultsOverrides {
		if value.ConfigurationDefaultOverrideValue != nil {
			result[key] = value.ConfigurationDefaultOverrideValue
		}
	}
	return result
}

// NotifyConfigurationSchemaUpdated notifies that configuration schema has been updated
func (r *ConfigurationRegistry) NotifyConfigurationSchemaUpdated(configurations ...*IConfigurationNode) {
	r.onDidSchemaChangeEmitter.Fire(nil)
}

// RegisterOverrideIdentifiers registers override identifiers
func (r *ConfigurationRegistry) RegisterOverrideIdentifiers(identifiers []string) {
	r.mu.Lock()
	defer r.mu.Unlock()

	r.doRegisterOverrideIdentifiers(identifiers)
	r.onDidSchemaChangeEmitter.Fire(nil)
}

// Helper functions
func stringPtr(s string) *string {
	return &s
}

func boolPtr(b bool) *bool {
	return &b
}

// createSetFromMap creates a Set from a map[string]bool
func (r *ConfigurationRegistry) createSetFromMap(m map[string]bool) *basecommon.Set[string] {
	set := basecommon.NewSet[string]()
	for key := range m {
		set.Add(key)
	}
	return set
}

// mergeDefaultConfigurationsForOverrideIdentifier merges default configurations for override identifiers
func (r *ConfigurationRegistry) mergeDefaultConfigurationsForOverrideIdentifier(key string, value interface{}, source *IExtensionInfo, existing *IConfigurationDefaultOverrideValue) *IConfigurationDefaultOverrideValue {
	// Simple implementation - in a real scenario this would be more complex
	return &IConfigurationDefaultOverrideValue{
		Value:  value,
		Source: source,
	}
}

// mergeDefaultConfigurationsForConfigurationProperty merges default configurations for configuration properties
func (r *ConfigurationRegistry) mergeDefaultConfigurationsForConfigurationProperty(key string, value interface{}, source *IExtensionInfo, existing *IConfigurationDefaultOverrideValue) *IConfigurationDefaultOverrideValue {
	// Simple implementation - in a real scenario this would be more complex
	return &IConfigurationDefaultOverrideValue{
		Value:  value,
		Source: source,
	}
}

// updateDefaultOverrideProperty updates a default override property
func (r *ConfigurationRegistry) updateDefaultOverrideProperty(key string, override *IConfigurationDefaultOverrideValue, source *IExtensionInfo) {
	// Update the default language configuration overrides node
	if r.defaultLanguageConfigurationOverridesNode.Properties == nil {
		r.defaultLanguageConfigurationOverridesNode.Properties = make(basecommon.IStringDictionary[*IConfigurationPropertySchema])
	}

	r.defaultLanguageConfigurationOverridesNode.Properties[key] = &IConfigurationPropertySchema{
		Type:        "object",
		Default:     override.Value,
		Description: stringPtr("Language-specific configuration override"),
	}
}

// updatePropertyDefaultValue updates the default value of a property
func (r *ConfigurationRegistry) updatePropertyDefaultValue(key string, property *IRegisteredConfigurationPropertySchema) {
	// Update the property's default value based on configuration defaults overrides
	if override := r.configurationDefaultsOverrides[key]; override != nil && override.ConfigurationDefaultOverrideValue != nil {
		property.DefaultDefaultValue = override.ConfigurationDefaultOverrideValue.Value
		property.DefaultValueSource = override.ConfigurationDefaultOverrideValue.Source
	}
}

// Note: overrideIdentifiersFromKey is already defined in configurationModels.go

// validateAndRegisterProperties validates and registers configuration properties
func (r *ConfigurationRegistry) validateAndRegisterProperties(configuration *IConfigurationNode, validate bool, extensionInfo *IExtensionInfo, restrictedProperties []string, scope ConfigurationScope, bucket map[string]bool) {
	if configuration.Properties != nil {
		for key, property := range configuration.Properties {
			if validate && !r.isValidProperty(key, property) {
				continue
			}

			bucket[key] = true

			// Create registered property schema
			registeredProperty := &IRegisteredConfigurationPropertySchema{
				IConfigurationPropertySchema: property,
				DefaultDefaultValue:          property.Default,
				Source:                       extensionInfo,
			}

			// Handle policy
			if property.Policy != nil {
				r.policyConfigurations[property.Policy.Name] = key
			}

			// Handle restricted properties
			isRestricted := false
			for _, restricted := range restrictedProperties {
				if restricted == key {
					isRestricted = true
					break
				}
			}

			if isRestricted {
				r.excludedConfigurationProperties[key] = registeredProperty
			} else {
				r.configurationProperties[key] = registeredProperty
				r.updateSchema(key, property)
			}
		}
	}

	if configuration.AllOf != nil {
		for _, node := range configuration.AllOf {
			r.validateAndRegisterProperties(node, validate, extensionInfo, restrictedProperties, scope, bucket)
		}
	}
}

// isValidProperty validates a configuration property
func (r *ConfigurationRegistry) isValidProperty(key string, property *IConfigurationPropertySchema) bool {
	// Basic validation - in a real implementation this would be more comprehensive
	return key != "" && property != nil
}

// registerJSONConfiguration registers a configuration with the JSON schema registry
func (r *ConfigurationRegistry) registerJSONConfiguration(configuration *IConfigurationNode) {
	// This would register the configuration with the JSON schema system
	// For now, we'll just update the resource language settings schema
	r.updateResourceLanguageSettingsSchema(configuration)
}

// updateResourceLanguageSettingsSchema updates the resource language settings schema
func (r *ConfigurationRegistry) updateResourceLanguageSettingsSchema(configuration *IConfigurationNode) {
	if configuration.Properties != nil {
		for key, property := range configuration.Properties {
			// Add property to resource language settings schema
			if r.resourceLanguageSettingsSchema.Properties == nil {
				r.resourceLanguageSettingsSchema.Properties = make(basecommon.IJSONSchemaMap)
			}

			jsonSchema := &basecommon.IJSONSchema{
				Type:        property.Type,
				Default:     property.Default,
				Description: property.Description,
			}

			r.resourceLanguageSettingsSchema.Properties[key] = jsonSchema
		}
	}
}

// updateSchema updates the schema for a property
func (r *ConfigurationRegistry) updateSchema(key string, property interface{}) {
	var configProperty *IConfigurationPropertySchema

	switch p := property.(type) {
	case *IConfigurationPropertySchema:
		configProperty = p
	case *IRegisteredConfigurationPropertySchema:
		configProperty = p.IConfigurationPropertySchema
	default:
		return
	}

	// Update the appropriate settings group based on scope
	scope := ConfigurationScopeWindow
	if configProperty.Scope != nil {
		scope = *configProperty.Scope
	}

	switch scope {
	case ConfigurationScopeApplication:
		ApplicationSettings.Properties[key] = configProperty
	case ConfigurationScopeMachine:
		MachineSettings.Properties[key] = configProperty
	case ConfigurationScopeMachineOverridable:
		MachineOverridableSettings.Properties[key] = configProperty
	case ConfigurationScopeResource:
		ResourceSettings.Properties[key] = configProperty
	case ConfigurationScopeLanguageOverridable:
		ResourceSettings.Properties[key] = configProperty // Language overridable goes to resource
	default:
		WindowSettings.Properties[key] = configProperty
	}

	AllSettings.Properties[key] = configProperty
}

// removeFromSchema removes a property from the schema
func (r *ConfigurationRegistry) removeFromSchema(key string, property *IConfigurationPropertySchema) {
	delete(AllSettings.Properties, key)
	delete(ApplicationSettings.Properties, key)
	delete(ApplicationMachineSettings.Properties, key)
	delete(MachineSettings.Properties, key)
	delete(MachineOverridableSettings.Properties, key)
	delete(WindowSettings.Properties, key)
	delete(ResourceSettings.Properties, key)

	if r.resourceLanguageSettingsSchema.Properties != nil {
		delete(r.resourceLanguageSettingsSchema.Properties, key)
	}
}

// doRegisterConfigurations is the internal method to register configurations
func (r *ConfigurationRegistry) doRegisterConfigurations(configurations []*IConfigurationNode, validate bool, bucket map[string]bool) {
	for _, configuration := range configurations {
		r.validateAndRegisterProperties(configuration, validate, configuration.ExtensionInfo, configuration.RestrictedProperties, ConfigurationScopeWindow, bucket)
		r.configurationContributors = append(r.configurationContributors, configuration)
		r.registerJSONConfiguration(configuration)
	}
}

// doDeregisterConfigurations is the internal method to deregister configurations
func (r *ConfigurationRegistry) doDeregisterConfigurations(configurations []*IConfigurationNode, bucket map[string]bool) {
	var deregisterConfiguration func(*IConfigurationNode)
	deregisterConfiguration = func(configuration *IConfigurationNode) {
		if configuration.Properties != nil {
			for key := range configuration.Properties {
				bucket[key] = true
				property := r.configurationProperties[key]
				if property != nil && property.Policy != nil && property.Policy.Name != "" {
					delete(r.policyConfigurations, property.Policy.Name)
				}
				delete(r.configurationProperties, key)
				r.removeFromSchema(key, configuration.Properties[key])
			}
		}
		if configuration.AllOf != nil {
			for _, node := range configuration.AllOf {
				deregisterConfiguration(node)
			}
		}
	}

	for _, configuration := range configurations {
		deregisterConfiguration(configuration)
		for i, contributor := range r.configurationContributors {
			if contributor == configuration {
				r.configurationContributors = append(r.configurationContributors[:i], r.configurationContributors[i+1:]...)
				break
			}
		}
	}
}

// doRegisterDefaultConfigurations is the internal method to register default configurations
func (r *ConfigurationRegistry) doRegisterDefaultConfigurations(configurationDefaults []*IConfigurationDefaults, bucket map[string]bool) {
	r.registeredConfigurationDefaults = append(r.registeredConfigurationDefaults, configurationDefaults...)

	for _, defaults := range configurationDefaults {
		for key, value := range defaults.Overrides {
			bucket[key] = true

			configurationDefaultOverridesForKey := r.configurationDefaultsOverrides[key]
			if configurationDefaultOverridesForKey == nil {
				configurationDefaultOverridesForKey = &struct {
					ConfigurationDefaultOverrides     []*IConfigurationDefaultOverride
					ConfigurationDefaultOverrideValue *IConfigurationDefaultOverrideValue
				}{
					ConfigurationDefaultOverrides: make([]*IConfigurationDefaultOverride, 0),
				}
				r.configurationDefaultsOverrides[key] = configurationDefaultOverridesForKey
			}

			configurationDefaultOverridesForKey.ConfigurationDefaultOverrides = append(
				configurationDefaultOverridesForKey.ConfigurationDefaultOverrides,
				&IConfigurationDefaultOverride{
					Value:  value,
					Source: defaults.Source,
				},
			)

			// Handle override identifiers
			if OverridePropertyRegex.MatchString(key) {
				newDefaultOverride := r.mergeDefaultConfigurationsForOverrideIdentifier(key, value, defaults.Source, configurationDefaultOverridesForKey.ConfigurationDefaultOverrideValue)
				if newDefaultOverride != nil {
					configurationDefaultOverridesForKey.ConfigurationDefaultOverrideValue = newDefaultOverride
					r.updateDefaultOverrideProperty(key, newDefaultOverride, defaults.Source)
					r.doRegisterOverrideIdentifiers(overrideIdentifiersFromKey(key))
				}
			} else {
				// Handle configuration properties
				newDefaultOverride := r.mergeDefaultConfigurationsForConfigurationProperty(key, value, defaults.Source, configurationDefaultOverridesForKey.ConfigurationDefaultOverrideValue)
				if newDefaultOverride != nil {
					configurationDefaultOverridesForKey.ConfigurationDefaultOverrideValue = newDefaultOverride
					property := r.configurationProperties[key]
					if property != nil {
						r.updatePropertyDefaultValue(key, property)
						r.updateSchema(key, property)
					}
				}
			}
		}
	}
}

// doDeregisterDefaultConfigurations is the internal method to deregister default configurations
func (r *ConfigurationRegistry) doDeregisterDefaultConfigurations(defaultConfigurations []*IConfigurationDefaults, bucket map[string]bool) {
	for _, defaultConfiguration := range defaultConfigurations {
		for i, registered := range r.registeredConfigurationDefaults {
			if registered == defaultConfiguration {
				r.registeredConfigurationDefaults = append(r.registeredConfigurationDefaults[:i], r.registeredConfigurationDefaults[i+1:]...)
				break
			}
		}
	}

	for _, defaults := range defaultConfigurations {
		for key := range defaults.Overrides {
			configurationDefaultOverridesForKey := r.configurationDefaultsOverrides[key]
			if configurationDefaultOverridesForKey == nil {
				continue
			}

			// Find and remove the override
			for i, override := range configurationDefaultOverridesForKey.ConfigurationDefaultOverrides {
				if (defaults.Source == nil && override.Source == nil) ||
					(defaults.Source != nil && override.Source != nil && defaults.Source.ID == override.Source.ID) {
					configurationDefaultOverridesForKey.ConfigurationDefaultOverrides = append(
						configurationDefaultOverridesForKey.ConfigurationDefaultOverrides[:i],
						configurationDefaultOverridesForKey.ConfigurationDefaultOverrides[i+1:]...)
					break
				}
			}

			if len(configurationDefaultOverridesForKey.ConfigurationDefaultOverrides) == 0 {
				delete(r.configurationDefaultsOverrides, key)
			}

			bucket[key] = true
		}
	}
	r.updateOverridePropertyPatternKey()
}

// doRegisterOverrideIdentifiers registers override identifiers
func (r *ConfigurationRegistry) doRegisterOverrideIdentifiers(identifiers []string) {
	for _, identifier := range identifiers {
		r.overrideIdentifiers[identifier] = true
	}
	r.updateOverridePropertyPatternKey()
}

// registerOverridePropertyPatternKey registers the override property pattern key
func (r *ConfigurationRegistry) registerOverridePropertyPatternKey() {
	schemaProperty := &IConfigurationPropertySchema{
		Type:        "object",
		Description: stringPtr(nlscommon.Localize("overrideSettings.defaultDescription", "Configure editor settings to be overridden for a language.")),
	}

	AllSettings.PatternProperties[OverridePropertyPattern] = schemaProperty
	ApplicationSettings.PatternProperties[OverridePropertyPattern] = schemaProperty
	ApplicationMachineSettings.PatternProperties[OverridePropertyPattern] = schemaProperty
	MachineSettings.PatternProperties[OverridePropertyPattern] = schemaProperty
	MachineOverridableSettings.PatternProperties[OverridePropertyPattern] = schemaProperty
	WindowSettings.PatternProperties[OverridePropertyPattern] = schemaProperty
	ResourceSettings.PatternProperties[OverridePropertyPattern] = schemaProperty

	r.onDidSchemaChangeEmitter.Fire(nil)
}

// updateOverridePropertyPatternKey updates the override property pattern key
func (r *ConfigurationRegistry) updateOverridePropertyPatternKey() {
	for identifier := range r.overrideIdentifiers {
		overrideIdentifierProperty := "[" + identifier + "]"
		schemaProperty := &IConfigurationPropertySchema{
			Type:        "object",
			Description: stringPtr(nlscommon.Localize("overrideSettings.defaultDescription", "Configure editor settings to be overridden for a language.")),
		}

		AllSettings.Properties[overrideIdentifierProperty] = schemaProperty
		ApplicationSettings.Properties[overrideIdentifierProperty] = schemaProperty
		ApplicationMachineSettings.Properties[overrideIdentifierProperty] = schemaProperty
		MachineSettings.Properties[overrideIdentifierProperty] = schemaProperty
		MachineOverridableSettings.Properties[overrideIdentifierProperty] = schemaProperty
		WindowSettings.Properties[overrideIdentifierProperty] = schemaProperty
		ResourceSettings.Properties[overrideIdentifierProperty] = schemaProperty
	}
}
