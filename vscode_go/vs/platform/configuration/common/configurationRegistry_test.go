/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"testing"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
)

func TestConfigurationRegistry_Basic(t *testing.T) {
	registry := NewConfigurationRegistry()

	// Test basic registry creation
	if registry == nil {
		t.Fatal("Registry should not be nil")
	}

	// Test getting configurations (should be empty initially except for default overrides)
	configs := registry.GetConfigurations()
	if len(configs) != 1 { // Should have the default language configuration overrides node
		t.Errorf("Expected 1 configuration, got %d", len(configs))
	}

	// Test getting configuration properties (should be empty initially)
	properties := registry.GetConfigurationProperties()
	if len(properties) != 0 {
		t.Errorf("Expected 0 properties, got %d", len(properties))
	}
}

func TestConfigurationRegistry_RegisterConfiguration(t *testing.T) {
	registry := NewConfigurationRegistry()

	// Create a test configuration
	testConfig := &IConfigurationNode{
		ID:    stringPtr("test.config"),
		Title: stringPtr("Test Configuration"),
		Properties: basecommon.IStringDictionary[*IConfigurationPropertySchema]{
			"test.property": {
				Type:        "string",
				Default:     "default value",
				Description: stringPtr("A test property"),
			},
		},
	}

	// Register the configuration
	result := registry.RegisterConfiguration(testConfig)

	// Verify the result
	if result != testConfig {
		t.Error("RegisterConfiguration should return the same configuration")
	}

	// Verify it was added to configurations
	configs := registry.GetConfigurations()
	found := false
	for _, config := range configs {
		if config.ID != nil && *config.ID == "test.config" {
			found = true
			break
		}
	}
	if !found {
		t.Error("Configuration was not added to configurations list")
	}

	// Verify the property was registered
	properties := registry.GetConfigurationProperties()
	if _, exists := properties["test.property"]; !exists {
		t.Error("Property was not registered")
	}
}

func TestConfigurationRegistry_RegisterDefaultConfigurations(t *testing.T) {
	registry := NewConfigurationRegistry()

	// Create test default configuration
	defaults := []*IConfigurationDefaults{
		{
			Overrides: basecommon.IStringDictionary[interface{}]{
				"test.setting": "override value",
			},
			Source: &IExtensionInfo{
				ID:          "test.extension",
				DisplayName: stringPtr("Test Extension"),
			},
		},
	}

	// Register the defaults
	registry.RegisterDefaultConfigurations(defaults)

	// Verify the defaults were registered
	registeredDefaults := registry.GetRegisteredDefaultConfigurations()
	if len(registeredDefaults) != 1 {
		t.Errorf("Expected 1 registered default, got %d", len(registeredDefaults))
	}

	// Verify the override was registered
	overrides := registry.GetConfigurationDefaultsOverrides()
	if _, exists := overrides["test.setting"]; !exists {
		t.Error("Default override was not registered")
	}
}

func TestConfigurationRegistry_Events(t *testing.T) {
	registry := NewConfigurationRegistry()

	// Register a configuration to trigger events
	testConfig := &IConfigurationNode{
		ID: stringPtr("test.event"),
		Properties: basecommon.IStringDictionary[*IConfigurationPropertySchema]{
			"test.event.property": {
				Type:    "boolean",
				Default: false,
			},
		},
	}

	registry.RegisterConfiguration(testConfig)

	// Verify the event methods exist and return valid events
	if registry.OnDidSchemaChange() == nil {
		t.Error("OnDidSchemaChange should return a valid event")
	}

	if registry.OnDidUpdateConfiguration() == nil {
		t.Error("OnDidUpdateConfiguration should return a valid event")
	}
}

func TestConfigurationRegistry_OverrideIdentifiers(t *testing.T) {
	registry := NewConfigurationRegistry()

	// Register override identifiers
	identifiers := []string{"typescript", "javascript", "python"}
	registry.RegisterOverrideIdentifiers(identifiers)

	// Verify the identifiers were registered (this is internal state,
	// so we can't directly test it, but we can verify no errors occurred)

	// The override identifiers should be available in the pattern properties
	if AllSettings.PatternProperties[OverridePropertyPattern] == nil {
		t.Error("Override property pattern should be registered")
	}
}
