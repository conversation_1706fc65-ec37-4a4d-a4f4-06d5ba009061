/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"encoding/json"
	"fmt"
	"strings"
	"sync"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	policycommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/policy/common"
	registrycommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/registry/common"
)

// Extensions constants for configuration registry
var Extensions = struct {
	Configuration string
}{
	Configuration: "base.contributions.configuration",
}

// IConfigurationRegistry interface for configuration registry operations
type IConfigurationRegistry interface {
	RegisterConfiguration(configuration *IConfigurationNode) *IConfigurationNode
	RegisterConfigurations(configurations []*IConfigurationNode, validate bool)
	DeregisterConfigurations(configurations []*IConfigurationNode)
	UpdateConfigurations(configurations *IConfigurationRegistryUpdate)
	RegisterDefaultConfigurations(defaultConfigurations []*IConfigurationDefaults)
	DeregisterDefaultConfigurations(defaultConfigurations []*IConfigurationDefaults)
	DeltaConfiguration(delta *IConfigurationDelta)
	GetRegisteredDefaultConfigurations() []*IConfigurationDefaults
	GetConfigurationDefaultsOverrides() map[string]*IConfigurationDefaultOverrideValue
	NotifyConfigurationSchemaUpdated(configurations ...*IConfigurationNode)
	OnDidSchemaChange() basecommon.Event[interface{}]
	OnDidUpdateConfiguration() basecommon.Event[*IConfigurationUpdateEvent]
	GetConfigurations() []*IConfigurationNode
	GetConfigurationProperties() basecommon.IStringDictionary[*IRegisteredConfigurationPropertySchema]
	GetPolicyConfigurations() map[string]string
	GetExcludedConfigurationProperties() basecommon.IStringDictionary[*IRegisteredConfigurationPropertySchema]
	RegisterOverrideIdentifiers(identifiers []string)
}

// IConfigurationNode represents a configuration node
type IConfigurationNode struct {
	ID                   *string                                                     `json:"id,omitempty"`
	Order                *int                                                        `json:"order,omitempty"`
	Type                 interface{}                                                 `json:"type,omitempty"` // string or []string
	Title                *string                                                     `json:"title,omitempty"`
	Description          *string                                                     `json:"description,omitempty"`
	Properties           basecommon.IStringDictionary[*IConfigurationPropertySchema] `json:"properties,omitempty"`
	AllOf                []*IConfigurationNode                                       `json:"allOf,omitempty"`
	Scope                *ConfigurationScope                                         `json:"scope,omitempty"`
	ExtensionInfo        *IExtensionInfo                                             `json:"extensionInfo,omitempty"`
	RestrictedProperties []string                                                    `json:"restrictedProperties,omitempty"`
}

// IExtensionInfo represents extension information
type IExtensionInfo struct {
	ID          string  `json:"id"`
	DisplayName *string `json:"displayName,omitempty"`
}

// IConfigurationPropertySchema represents a configuration property schema
type IConfigurationPropertySchema struct {
	Type               interface{}         `json:"type,omitempty"` // string or []string
	Default            interface{}         `json:"default,omitempty"`
	Description        *string             `json:"description,omitempty"`
	Policy             *IPolicyInfo        `json:"policy,omitempty"`
	Scope              *ConfigurationScope `json:"scope,omitempty"`
	Restricted         *bool               `json:"restricted,omitempty"`
	Included           *bool               `json:"included,omitempty"`
	Enum               []string            `json:"enum,omitempty"`
	EnumDescriptions   []string            `json:"enumDescriptions,omitempty"`
	DeprecationMessage *string             `json:"deprecationMessage,omitempty"`
	Tags               []string            `json:"tags,omitempty"`
}

// IPolicyInfo represents policy information for configuration
type IPolicyInfo struct {
	Name             string      `json:"name"`
	DefaultValue     interface{} `json:"defaultValue,omitempty"`
	PreviewFeature   *bool       `json:"previewFeature,omitempty"`
	MinimumVersion   *string     `json:"minimumVersion,omitempty"`
}

// IRegisteredConfigurationPropertySchema extends IConfigurationPropertySchema with registration info
type IRegisteredConfigurationPropertySchema struct {
	*IConfigurationPropertySchema
	DefaultDefaultValue interface{}     `json:"defaultDefaultValue,omitempty"`
	Source              *IExtensionInfo `json:"source,omitempty"`
	DefaultValueSource  interface{}     `json:"defaultValueSource,omitempty"` // IExtensionInfo or map[string]*IExtensionInfo
}

// IConfigurationDefaults represents configuration defaults
type IConfigurationDefaults struct {
	Overrides basecommon.IStringDictionary[interface{}] `json:"overrides"`
	Source    *IExtensionInfo                           `json:"source,omitempty"`
}

// IConfigurationDefaultOverrideValue represents default override value
type IConfigurationDefaultOverrideValue struct {
	Value  interface{} `json:"value"`
	Source interface{} `json:"source"` // IExtensionInfo or map[string]*IExtensionInfo
}

// IConfigurationRegistryUpdate represents a configuration registry update
type IConfigurationRegistryUpdate struct {
	Add    []*IConfigurationNode `json:"add,omitempty"`
	Remove []*IConfigurationNode `json:"remove,omitempty"`
}

// IConfigurationDelta represents configuration delta changes
type IConfigurationDelta struct {
	AddedConfigurations   []*IConfigurationNode     `json:"addedConfigurations,omitempty"`
	RemovedConfigurations []*IConfigurationNode     `json:"removedConfigurations,omitempty"`
	AddedDefaults         []*IConfigurationDefaults `json:"addedDefaults,omitempty"`
	RemovedDefaults       []*IConfigurationDefaults `json:"removedDefaults,omitempty"`
}

// IConfigurationUpdateEvent represents configuration update events
type IConfigurationUpdateEvent struct {
	Properties        *basecommon.Set[string] `json:"properties"`
	DefaultsOverrides *bool                   `json:"defaultsOverrides,omitempty"`
}

// DefaultConfiguration manages default configuration values
type DefaultConfiguration struct {
	*basecommon.Disposable

	onDidChangeConfiguration *basecommon.Emitter[*DefaultConfigurationChangeEvent]
	configurationModel       *ConfigurationModel
	logService               logcommon.ILogService
	mu                       sync.RWMutex
}

// DefaultConfigurationChangeEvent represents default configuration change events
type DefaultConfigurationChangeEvent struct {
	Defaults   *ConfigurationModel `json:"defaults"`
	Properties []string            `json:"properties"`
}

// NewDefaultConfiguration creates a new DefaultConfiguration
func NewDefaultConfiguration(logService logcommon.ILogService) *DefaultConfiguration {
	dc := &DefaultConfiguration{
		Disposable:               basecommon.NewDisposable(),
		onDidChangeConfiguration: basecommon.NewEmitter[*DefaultConfigurationChangeEvent](),
		logService:               logService,
	}

	dc.configurationModel = CreateEmptyModel(logService)
	return dc
}

// OnDidChangeConfiguration returns the change event
func (dc *DefaultConfiguration) OnDidChangeConfiguration() basecommon.Event[*DefaultConfigurationChangeEvent] {
	return dc.onDidChangeConfiguration.Event()
}

// GetConfigurationModel returns the current configuration model
func (dc *DefaultConfiguration) GetConfigurationModel() *ConfigurationModel {
	dc.mu.RLock()
	defer dc.mu.RUnlock()
	return dc.configurationModel
}

// Initialize initializes the default configuration
func (dc *DefaultConfiguration) Initialize() (*ConfigurationModel, error) {
	dc.resetConfigurationModel()

	// Register for configuration registry changes
	registry := registrycommon.Registry.As(Extensions.Configuration)
	if configRegistry, ok := registry.(IConfigurationRegistry); ok {
		dc.Register(configRegistry.OnDidUpdateConfiguration().Subscribe(func(event *IConfigurationUpdateEvent) {
			if event.Properties != nil {
				properties := event.Properties.ToSlice()
				dc.onDidUpdateConfiguration(properties, event.DefaultsOverrides != nil && *event.DefaultsOverrides)
			}
		}))
	}

	return dc.GetConfigurationModel(), nil
}

// Reload reloads the configuration
func (dc *DefaultConfiguration) Reload() *ConfigurationModel {
	dc.resetConfigurationModel()
	return dc.GetConfigurationModel()
}

// onDidUpdateConfiguration handles configuration updates
func (dc *DefaultConfiguration) onDidUpdateConfiguration(properties []string, defaultsOverrides bool) {
	registry := registrycommon.Registry.As(Extensions.Configuration)
	if configRegistry, ok := registry.(IConfigurationRegistry); ok {
		dc.updateConfigurationModel(properties, configRegistry.GetConfigurationProperties())
		event := &DefaultConfigurationChangeEvent{
			Defaults:   dc.GetConfigurationModel(),
			Properties: properties,
		}
		dc.onDidChangeConfiguration.Fire(event)
	}
}

// getConfigurationDefaultOverrides gets configuration default overrides
func (dc *DefaultConfiguration) getConfigurationDefaultOverrides() basecommon.IStringDictionary[interface{}] {
	// Default implementation returns empty map - can be overridden by subclasses
	return make(basecommon.IStringDictionary[interface{}])
}

// resetConfigurationModel resets the configuration model
func (dc *DefaultConfiguration) resetConfigurationModel() {
	dc.mu.Lock()
	defer dc.mu.Unlock()

	dc.configurationModel = CreateEmptyModel(dc.logService)

	registry := registrycommon.Registry.As(Extensions.Configuration)
	if configRegistry, ok := registry.(IConfigurationRegistry); ok {
		properties := configRegistry.GetConfigurationProperties()
		propertyKeys := make([]string, 0, len(properties))
		for key := range properties {
			propertyKeys = append(propertyKeys, key)
		}
		dc.updateConfigurationModel(propertyKeys, properties)
	}
}

// updateConfigurationModel updates the configuration model
func (dc *DefaultConfiguration) updateConfigurationModel(properties []string, configurationProperties basecommon.IStringDictionary[*IRegisteredConfigurationPropertySchema]) {
	configurationDefaultsOverrides := dc.getConfigurationDefaultOverrides()

	for _, key := range properties {
		defaultOverrideValue, hasOverride := configurationDefaultsOverrides[key]
		propertySchema, hasProperty := configurationProperties[key]

		if hasOverride {
			dc.configurationModel.SetValue(key, defaultOverrideValue)
		} else if hasProperty && propertySchema != nil {
			dc.configurationModel.SetValue(key, basecommon.DeepClone(propertySchema.Default))
		} else {
			dc.configurationModel.RemoveValue(key)
		}
	}
}

// Dispose disposes the default configuration
func (dc *DefaultConfiguration) Dispose() {
	if dc.Disposable != nil {
		dc.Disposable.Dispose()
	}
	if dc.onDidChangeConfiguration != nil {
		dc.onDidChangeConfiguration.Dispose()
	}
}

// IPolicyConfiguration interface for policy configuration
type IPolicyConfiguration interface {
	OnDidChangeConfiguration() basecommon.Event[*ConfigurationModel]
	GetConfigurationModel() *ConfigurationModel
	Initialize() (*ConfigurationModel, error)
}

// NullPolicyConfiguration is a null implementation of IPolicyConfiguration
type NullPolicyConfiguration struct {
	configurationModel *ConfigurationModel
}

// NewNullPolicyConfiguration creates a new NullPolicyConfiguration
func NewNullPolicyConfiguration() *NullPolicyConfiguration {
	return &NullPolicyConfiguration{
		configurationModel: CreateEmptyModel(logcommon.NewNullLogService()),
	}
}

// OnDidChangeConfiguration returns no events for null implementation
func (npc *NullPolicyConfiguration) OnDidChangeConfiguration() basecommon.Event[*ConfigurationModel] {
	return basecommon.EventNone[*ConfigurationModel]()
}

// GetConfigurationModel returns the empty configuration model
func (npc *NullPolicyConfiguration) GetConfigurationModel() *ConfigurationModel {
	return npc.configurationModel
}

// Initialize returns the configuration model
func (npc *NullPolicyConfiguration) Initialize() (*ConfigurationModel, error) {
	return npc.configurationModel, nil
}

// PolicyConfiguration manages policy-based configuration
type PolicyConfiguration struct {
	*basecommon.Disposable

	onDidChangeConfiguration *basecommon.Emitter[*ConfigurationModel]
	configurationModel       *ConfigurationModel
	defaultConfiguration     *DefaultConfiguration
	policyService            policycommon.IPolicyService
	logService               logcommon.ILogService
	configurationRegistry    IConfigurationRegistry
	mu                       sync.RWMutex
}

// NewPolicyConfiguration creates a new PolicyConfiguration
func NewPolicyConfiguration(defaultConfiguration *DefaultConfiguration, policyService policycommon.IPolicyService, logService logcommon.ILogService) *PolicyConfiguration {
	pc := &PolicyConfiguration{
		Disposable:               basecommon.NewDisposable(),
		onDidChangeConfiguration: basecommon.NewEmitter[*ConfigurationModel](),
		defaultConfiguration:     defaultConfiguration,
		policyService:            policyService,
		logService:               logService,
	}

	pc.configurationModel = CreateEmptyModel(logService)

	registry := registrycommon.Registry.As(Extensions.Configuration)
	if configRegistry, ok := registry.(IConfigurationRegistry); ok {
		pc.configurationRegistry = configRegistry
	}

	return pc
}

// OnDidChangeConfiguration returns the change event
func (pc *PolicyConfiguration) OnDidChangeConfiguration() basecommon.Event[*ConfigurationModel] {
	return pc.onDidChangeConfiguration.Event()
}

// GetConfigurationModel returns the current configuration model
func (pc *PolicyConfiguration) GetConfigurationModel() *ConfigurationModel {
	pc.mu.RLock()
	defer pc.mu.RUnlock()
	return pc.configurationModel
}

// Initialize initializes the policy configuration
func (pc *PolicyConfiguration) Initialize() (*ConfigurationModel, error) {
	pc.logService.Trace("PolicyConfiguration#initialize")

	// Update policy definitions for default configuration keys
	defaultKeys := pc.defaultConfiguration.GetConfigurationModel().GetKeys()
	keys1, err := pc.updatePolicyDefinitions(defaultKeys)
	if err != nil {
		return nil, err
	}

	// Update policy definitions for excluded configuration properties
	if pc.configurationRegistry != nil {
		excludedProperties := pc.configurationRegistry.GetExcludedConfigurationProperties()
		excludedKeys := make([]string, 0, len(excludedProperties))
		for key := range excludedProperties {
			excludedKeys = append(excludedKeys, key)
		}
		keys2, err := pc.updatePolicyDefinitions(excludedKeys)
		if err != nil {
			return nil, err
		}
		keys1 = append(keys1, keys2...)
	}

	pc.update(keys1, false)

	// Register for policy service changes
	pc.Register(pc.policyService.OnDidChange().Subscribe(func(event []string) {
		pc.onDidChangePolicies(event)
	}))

	// Register for default configuration changes
	pc.Register(pc.defaultConfiguration.OnDidChangeConfiguration().Subscribe(func(event *DefaultConfigurationChangeEvent) {
		keys, err := pc.updatePolicyDefinitions(event.Properties)
		if err != nil {
			pc.logService.Error("Error updating policy definitions:", err)
			return
		}
		pc.update(keys, true)
	}))

	return pc.GetConfigurationModel(), nil
}

// updatePolicyDefinitions updates policy definitions for the given properties
func (pc *PolicyConfiguration) updatePolicyDefinitions(properties []string) ([]string, error) {
	pc.logService.Trace("PolicyConfiguration#updatePolicyDefinitions", strings.Join(properties, ", "))

	policyDefinitions := make(basecommon.IStringDictionary[*policycommon.PolicyDefinition])
	keys := make([]string, 0)

	var configurationProperties basecommon.IStringDictionary[*IRegisteredConfigurationPropertySchema]
	var excludedConfigurationProperties basecommon.IStringDictionary[*IRegisteredConfigurationPropertySchema]

	if pc.configurationRegistry != nil {
		configurationProperties = pc.configurationRegistry.GetConfigurationProperties()
		excludedConfigurationProperties = pc.configurationRegistry.GetExcludedConfigurationProperties()
	}

	for _, key := range properties {
		var config *IRegisteredConfigurationPropertySchema

		if configurationProperties != nil {
			if prop, exists := configurationProperties[key]; exists {
				config = prop
			}
		}

		if config == nil && excludedConfigurationProperties != nil {
			if prop, exists := excludedConfigurationProperties[key]; exists {
				config = prop
			}
		}

		if config == nil {
			// Config is removed. Add it to the list in case it was registered as policy before
			keys = append(keys, key)
			continue
		}

		if config.Policy != nil {
			// Validate policy type
			typeStr := ""
			if config.Type != nil {
				if str, ok := config.Type.(string); ok {
					typeStr = str
				}
			}

			if typeStr != "string" && typeStr != "number" && typeStr != "array" && typeStr != "object" && typeStr != "boolean" {
				pc.logService.Warn(fmt.Sprintf("Policy %s has unsupported type %s", config.Policy.Name, typeStr))
				continue
			}

			keys = append(keys, key)

			policyType := "string"
			if typeStr == "number" {
				policyType = "number"
			} else if typeStr == "boolean" {
				policyType = "boolean"
			}

			policyDefinitions[config.Policy.Name] = &policycommon.PolicyDefinition{
				Type:           policyType,
				PreviewFeature: config.Policy.PreviewFeature,
				DefaultValue:   config.Policy.DefaultValue,
			}
		}
	}

	if len(policyDefinitions) > 0 {
		_, err := pc.policyService.UpdatePolicyDefinitions(policyDefinitions)
		if err != nil {
			return keys, err
		}
	}

	return keys, nil
}

// onDidChangePolicies handles policy changes
func (pc *PolicyConfiguration) onDidChangePolicies(policyNames []string) {
	pc.logService.Trace("PolicyConfiguration#onDidChangePolicies", strings.Join(policyNames, ", "))

	if pc.configurationRegistry == nil {
		return
	}

	policyConfigurations := pc.configurationRegistry.GetPolicyConfigurations()
	keys := make([]string, 0)

	for _, policyName := range policyNames {
		if key, exists := policyConfigurations[policyName]; exists {
			keys = append(keys, key)
		}
	}

	pc.update(keys, true)
}

// update updates the policy configuration
func (pc *PolicyConfiguration) update(keys []string, trigger bool) {
	if len(keys) == 0 {
		return
	}

	pc.logService.Trace("PolicyConfiguration#update", strings.Join(keys, ", "))

	var configurationProperties basecommon.IStringDictionary[*IRegisteredConfigurationPropertySchema]
	var excludedConfigurationProperties basecommon.IStringDictionary[*IRegisteredConfigurationPropertySchema]

	if pc.configurationRegistry != nil {
		configurationProperties = pc.configurationRegistry.GetConfigurationProperties()
		excludedConfigurationProperties = pc.configurationRegistry.GetExcludedConfigurationProperties()
	}

	changed := make([][2]interface{}, 0)
	wasEmpty := pc.configurationModel.IsEmpty()

	for _, keyStr := range keys {
		var property *IRegisteredConfigurationPropertySchema

		if configurationProperties != nil {
			if prop, exists := configurationProperties[keyStr]; exists {
				property = prop
			}
		}

		if property == nil && excludedConfigurationProperties != nil {
			if prop, exists := excludedConfigurationProperties[keyStr]; exists {
				property = prop
			}
		}

		var policyName string
		if property != nil && property.Policy != nil {
			policyName = property.Policy.Name
		}

		if policyName != "" {
			policyValue := pc.policyService.GetPolicyValue(policyName)

			// Handle string policy values that need parsing
			if policyValueStr, ok := policyValue.(string); ok && property.Type != "string" {
				parsed, err := pc.parse(policyValueStr)
				if err != nil {
					pc.logService.Error(fmt.Sprintf("Error parsing policy value %s: %v", policyName, err))
					continue
				}
				policyValue = parsed
			}

			currentValue := pc.configurationModel.GetValue(keyStr)
			if wasEmpty {
				if policyValue != nil {
					changed = append(changed, [2]interface{}{keyStr, policyValue})
				}
			} else if !basecommon.Equals(currentValue, policyValue) {
				changed = append(changed, [2]interface{}{keyStr, policyValue})
			}
		} else {
			currentValue := pc.configurationModel.GetValue(keyStr)
			if currentValue != nil {
				changed = append(changed, [2]interface{}{keyStr, nil})
			}
		}
	}

	if len(changed) > 0 {
		pc.logService.Trace("PolicyConfiguration#changed", fmt.Sprintf("%v", changed))

		pc.mu.Lock()
		old := pc.configurationModel
		pc.configurationModel = CreateEmptyModel(pc.logService)

		// Copy existing values
		for _, key := range old.GetKeys() {
			pc.configurationModel.SetValue(key, old.GetValue(key))
		}

		// Apply changes
		for _, change := range changed {
			key := change[0].(string)
			value := change[1]
			if value == nil {
				pc.configurationModel.RemoveValue(key)
			} else {
				pc.configurationModel.SetValue(key, value)
			}
		}
		pc.mu.Unlock()

		if trigger {
			pc.onDidChangeConfiguration.Fire(pc.configurationModel)
		}
	}
}

// parse parses a JSON string into an object
func (pc *PolicyConfiguration) parse(content string) (interface{}, error) {
	if content == "" {
		return map[string]interface{}{}, nil
	}

	var result interface{}
	err := json.Unmarshal([]byte(content), &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// Dispose disposes the policy configuration
func (pc *PolicyConfiguration) Dispose() {
	if pc.Disposable != nil {
		pc.Disposable.Dispose()
	}
	if pc.onDidChangeConfiguration != nil {
		pc.onDidChangeConfiguration.Dispose()
	}
}
