/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package electronmain

import (
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	environmentcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment/common"
	filescommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	instantiationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	statenode "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/state/node"
	uriidentitycommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/uriIdentity/common"
	userDataProfileCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/userDataProfile/common"
	userDataProfileNode "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/userDataProfile/node"
	workspacecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/workspace/common"
)

// IUserDataProfilesMainService extends IUserDataProfilesService with main process specific functionality
type IUserDataProfilesMainService interface {
	userDataProfileCommon.IUserDataProfilesService

	// GetProfileForWorkspace returns the profile associated with a workspace
	GetProfileForWorkspace(workspaceIdentifier workspacecommon.IAnyWorkspaceIdentifier) *userDataProfileCommon.IUserDataProfile

	// UnsetWorkspace removes the association between a workspace and profile
	UnsetWorkspace(workspaceIdentifier workspacecommon.IAnyWorkspaceIdentifier, transient bool)

	// GetAssociatedEmptyWindows returns all empty windows associated with profiles
	GetAssociatedEmptyWindows() []workspacecommon.IEmptyWorkspaceIdentifier

	// CleanUpTransientProfiles cleans up transient profiles that are no longer associated
	CleanUpTransientProfiles() error

	// OnWillCreateProfile event fired before creating a profile
	OnWillCreateProfile() basecommon.Event[*userDataProfileCommon.WillCreateProfileEvent]

	// OnWillRemoveProfile event fired before removing a profile
	OnWillRemoveProfile() basecommon.Event[*userDataProfileCommon.WillRemoveProfileEvent]
}

// UserDataProfilesMainService implements IUserDataProfilesMainService for the main process
type UserDataProfilesMainService struct {
	*userDataProfileNode.UserDataProfilesService

	// Services
	stateService       statenode.IStateService
	uriIdentityService uriidentitycommon.IUriIdentityService
	environmentService environmentcommon.INativeEnvironmentService
	fileService        filescommon.IFileService
	logService         logcommon.ILogService
}

// NewUserDataProfilesMainService creates a new main process user data profiles service
func NewUserDataProfilesMainService(
	stateService statenode.IStateService,
	uriIdentityService uriidentitycommon.IUriIdentityService,
	environmentService environmentcommon.INativeEnvironmentService,
	fileService filescommon.IFileService,
	logService logcommon.ILogService,
) *UserDataProfilesMainService {
	service := &UserDataProfilesMainService{
		stateService:       stateService,
		uriIdentityService: uriIdentityService,
		environmentService: environmentService,
		fileService:        fileService,
		logService:         logService,
	}

	// Initialize the base service
	service.UserDataProfilesService = userDataProfileNode.NewUserDataProfilesService(
		stateService,
		uriIdentityService,
		environmentService,
		fileService,
		logService,
	)

	return service
}

// ServiceBrand implements the service brand
func (s *UserDataProfilesMainService) ServiceBrand() interface{} {
	return "userDataProfilesMainService"
}

// GetProfileForWorkspace returns the profile associated with a workspace
func (s *UserDataProfilesMainService) GetProfileForWorkspace(workspaceIdentifier workspacecommon.IAnyWorkspaceIdentifier) *userDataProfileCommon.IUserDataProfile {
	// Get the profile associations
	associations := s.GetStoredProfileAssociations()
	if associations == nil {
		return nil
	}

	// Check workspace associations
	if associations.Workspaces != nil {
		// Convert workspace identifier to string key for lookup
		workspaceKey := s.getWorkspaceKey(workspaceIdentifier)
		if profileID, exists := associations.Workspaces[workspaceKey]; exists {
			// Find the profile by ID
			profiles := s.Profiles()
			for _, profile := range profiles {
				if profile.ID == profileID {
					return profile
				}
			}
		}
	}

	// Check empty window associations
	if emptyWorkspace, ok := workspaceIdentifier.(*workspacecommon.IEmptyWorkspaceIdentifier); ok {
		if associations.EmptyWindows != nil {
			if profileID, exists := associations.EmptyWindows[emptyWorkspace.ID]; exists {
				// Find the profile by ID
				profiles := s.Profiles()
				for _, profile := range profiles {
					if profile.ID == profileID {
						return profile
					}
				}
			}
		}
	}

	return nil
}

// UnsetWorkspace removes the association between a workspace and profile
func (s *UserDataProfilesMainService) UnsetWorkspace(workspaceIdentifier workspacecommon.IAnyWorkspaceIdentifier, transient bool) {
	// Get current associations
	associations := s.GetStoredProfileAssociations()
	if associations == nil {
		return
	}

	modified := false

	// Handle empty workspace
	if emptyWorkspace, ok := workspaceIdentifier.(*workspacecommon.IEmptyWorkspaceIdentifier); ok {
		if associations.EmptyWindows != nil {
			if _, exists := associations.EmptyWindows[emptyWorkspace.ID]; exists {
				delete(associations.EmptyWindows, emptyWorkspace.ID)
				modified = true
			}
		}
	} else {
		// Handle regular workspace
		if associations.Workspaces != nil {
			workspaceKey := s.getWorkspaceKey(workspaceIdentifier)
			if _, exists := associations.Workspaces[workspaceKey]; exists {
				delete(associations.Workspaces, workspaceKey)
				modified = true
			}
		}
	}

	// Save if modified
	if modified {
		s.SaveStoredProfileAssociations(associations)
	}
}

// GetAssociatedEmptyWindows returns all empty windows associated with profiles
func (s *UserDataProfilesMainService) GetAssociatedEmptyWindows() []workspacecommon.IEmptyWorkspaceIdentifier {
	var emptyWindows []workspacecommon.IEmptyWorkspaceIdentifier

	// Get the profile associations
	associations := s.GetStoredProfileAssociations()
	if associations == nil || associations.EmptyWindows == nil {
		return emptyWindows
	}

	// Convert map keys to empty workspace identifiers
	for id := range associations.EmptyWindows {
		emptyWorkspace := &workspacecommon.IEmptyWorkspaceIdentifier{}
		emptyWorkspace.ID = id
		emptyWindows = append(emptyWindows, *emptyWorkspace)
	}

	return emptyWindows
}

// OnWillCreateProfile returns the event for profile creation
func (s *UserDataProfilesMainService) OnWillCreateProfile() basecommon.Event[*userDataProfileCommon.WillCreateProfileEvent] {
	return s.UserDataProfilesService.OnWillCreateProfile()
}

// OnWillRemoveProfile returns the event for profile removal
func (s *UserDataProfilesMainService) OnWillRemoveProfile() basecommon.Event[*userDataProfileCommon.WillRemoveProfileEvent] {
	return s.UserDataProfilesService.OnWillRemoveProfile()
}

// getWorkspaceKey generates a string key for a workspace identifier
func (s *UserDataProfilesMainService) getWorkspaceKey(workspaceIdentifier workspacecommon.IAnyWorkspaceIdentifier) string {
	switch workspace := workspaceIdentifier.(type) {
	case *workspacecommon.ISingleFolderWorkspaceIdentifier:
		return workspace.URI.ToString()
	case *workspacecommon.IWorkspaceIdentifier:
		return workspace.ConfigPath.ToString()
	case *workspacecommon.IEmptyWorkspaceIdentifier:
		return workspace.ID
	default:
		return ""
	}
}

// Service identifier for dependency injection
var IUserDataProfilesMainServiceID = instantiationcommon.CreateDecorator[IUserDataProfilesMainService]("userDataProfilesMainService")
