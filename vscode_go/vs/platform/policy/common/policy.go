package common

import "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"

// PolicyName represents a policy name
type PolicyName string

// IPolicy represents a policy interface
type IPolicy struct {
	Name PolicyName `json:"name"`
}

// IPolicyService is the interface for the policy service.
type IPolicyService interface {
	OnDidChange() common.Event[[]string]
	UpdatePolicyDefinitions(definitions map[string]*PolicyDefinition) (map[string]interface{}, error)
	GetPolicyValue(name string) interface{}
	Serialize() map[string]struct {
		Definition PolicyDefinition
		Value      interface{}
	}
	GetPolicyDefinitions() map[string]PolicyDefinition
}

// PolicyDefinition represents a policy definition.
type PolicyDefinition struct {
	Type           string
	PreviewFeature *bool
	DefaultValue   interface{}
}
