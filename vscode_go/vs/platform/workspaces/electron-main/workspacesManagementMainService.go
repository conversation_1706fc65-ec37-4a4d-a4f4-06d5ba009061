/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package electronmain

import (
	"github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	instantiationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
	windowselectronmain "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/window/electron-main"
	workspacecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/workspace/common"
	workspacescommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/workspaces/common"
)

var IWorkspacesManagementMainServiceID = instantiationcommon.CreateDecorator[IWorkspacesManagementMainService]("workspacesManagementMainService")

// IWorkspaceEnteredEvent is an event that is fired when a workspace is entered.
type IWorkspaceEnteredEvent struct {
	Window    windowselectronmain.ICodeWindow
	Workspace *workspacecommon.IWorkspaceIdentifier
}

// IWorkspacesManagementMainService is the service for managing workspaces.
type IWorkspacesManagementMainService interface {
	OnDidDeleteUntitledWorkspace() common.Event[*workspacecommon.IWorkspaceIdentifier]
	OnDidEnterWorkspace() common.Event[IWorkspaceEnteredEvent]

EnterWorkspace(intoWindow windowselectronmain.ICodeWindow, openedWindows []windowselectronmain.ICodeWindow, path *common.URI) (*workspacescommon.IEnterWorkspaceResult, error)

	CreateUntitledWorkspace(folders []*workspacescommon.IWorkspaceFolderCreationData, remoteAuthority string) (*workspacecommon.IWorkspaceIdentifier, error)

	DeleteUntitledWorkspace(workspace *workspacecommon.IWorkspaceIdentifier) error

	GetUntitledWorkspaces() []*workspacescommon.IUntitledWorkspaceInfo
	IsUntitledWorkspace(workspace *workspacecommon.IWorkspaceIdentifier) bool

	ResolveLocalWorkspace(path *common.URI) (*workspacecommon.IResolvedWorkspace, error)

	GetWorkspaceIdentifier(workspacePath *common.URI) (*workspacecommon.IWorkspaceIdentifier, error)
}
