/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"sync"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	lifecyclecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
)

// Extensions for JSON contribution registry
var Extensions = struct {
	JSONContribution string
}{
	JSONContribution: "base.contributions.jsonschemas",
}

// ISchemaContributions represents schema contributions
type ISchemaContributions struct {
	Schemas map[string]*basecommon.IJSONSchema `json:"schemas"`
}

// IJSONContributionRegistry represents the JSON contribution registry interface
type IJSONContributionRegistry interface {
	OnDidChangeSchema() basecommon.Event[string]
	OnDidChangeSchemaAssociations() basecommon.Event[interface{}]
	RegisterSchema(uri string, unresolvedSchemaContent *basecommon.IJSONSchema, store *lifecyclecommon.DisposableStore)
	RegisterSchemaAssociation(uri string, glob string) basecommon.IDisposable
	NotifySchemaChanged(uri string)
	GetSchemaContributions() *ISchemaContributions
	GetSchemaContent(uri string) *string
	HasSchemaContent(uri string) bool
	GetSchemaAssociations() map[string][]string
}

// JSONContributionRegistry implements IJSONContributionRegistry
type JSONContributionRegistry struct {
	schemasById          map[string]*basecommon.IJSONSchema
	schemaAssociations   map[string][]string
	onDidChangeSchema    *basecommon.Emitter[string]
	onDidChangeSchemaAssociations *basecommon.Emitter[interface{}]
	mu                   sync.RWMutex
}

// NewJSONContributionRegistry creates a new JSON contribution registry
func NewJSONContributionRegistry() *JSONContributionRegistry {
	return &JSONContributionRegistry{
		schemasById:                   make(map[string]*basecommon.IJSONSchema),
		schemaAssociations:           make(map[string][]string),
		onDidChangeSchema:            basecommon.NewEmitter[string](),
		onDidChangeSchemaAssociations: basecommon.NewEmitter[interface{}](),
	}
}

// OnDidChangeSchema returns the event for schema changes
func (r *JSONContributionRegistry) OnDidChangeSchema() basecommon.Event[string] {
	return r.onDidChangeSchema.Event()
}

// OnDidChangeSchemaAssociations returns the event for schema association changes
func (r *JSONContributionRegistry) OnDidChangeSchemaAssociations() basecommon.Event[interface{}] {
	return r.onDidChangeSchemaAssociations.Event()
}

// normalizeId normalizes a schema ID
func normalizeId(id string) string {
	// Simple normalization - in a real implementation this might be more complex
	return id
}

// RegisterSchema registers a schema to the registry
func (r *JSONContributionRegistry) RegisterSchema(uri string, unresolvedSchemaContent *basecommon.IJSONSchema, store *lifecyclecommon.DisposableStore) {
	r.mu.Lock()
	defer r.mu.Unlock()

	normalizedUri := normalizeId(uri)
	r.schemasById[normalizedUri] = unresolvedSchemaContent
	
	// Fire the change event after releasing the lock
	go r.onDidChangeSchema.Fire(uri)

	if store != nil {
		store.Register(lifecyclecommon.ToDisposable(func() {
			r.mu.Lock()
			delete(r.schemasById, normalizedUri)
			r.mu.Unlock()
			r.onDidChangeSchema.Fire(uri)
		}))
	}
}

// RegisterSchemaAssociation registers a schema association
func (r *JSONContributionRegistry) RegisterSchemaAssociation(uri string, glob string) basecommon.IDisposable {
	r.mu.Lock()
	defer r.mu.Unlock()

	normalizedUri := normalizeId(uri)
	if r.schemaAssociations[normalizedUri] == nil {
		r.schemaAssociations[normalizedUri] = make([]string, 0)
	}
	
	// Check if glob already exists
	for _, existing := range r.schemaAssociations[normalizedUri] {
		if existing == glob {
			// Already exists, return a no-op disposable
			return lifecyclecommon.ToDisposable(func() {})
		}
	}
	
	r.schemaAssociations[normalizedUri] = append(r.schemaAssociations[normalizedUri], glob)
	
	// Fire the change event after releasing the lock
	go r.onDidChangeSchemaAssociations.Fire(nil)

	return lifecyclecommon.ToDisposable(func() {
		r.mu.Lock()
		defer r.mu.Unlock()
		
		associations := r.schemaAssociations[normalizedUri]
		if associations != nil {
			for i, existing := range associations {
				if existing == glob {
					// Remove the association
					r.schemaAssociations[normalizedUri] = append(associations[:i], associations[i+1:]...)
					if len(r.schemaAssociations[normalizedUri]) == 0 {
						delete(r.schemaAssociations, normalizedUri)
					}
					r.onDidChangeSchemaAssociations.Fire(nil)
					break
				}
			}
		}
	})
}

// NotifySchemaChanged notifies that a schema has changed
func (r *JSONContributionRegistry) NotifySchemaChanged(uri string) {
	r.onDidChangeSchema.Fire(uri)
}

// GetSchemaContributions returns all schema contributions
func (r *JSONContributionRegistry) GetSchemaContributions() *ISchemaContributions {
	r.mu.RLock()
	defer r.mu.RUnlock()

	// Create a copy of the schemas map
	schemas := make(map[string]*basecommon.IJSONSchema)
	for k, v := range r.schemasById {
		schemas[k] = v
	}

	return &ISchemaContributions{
		Schemas: schemas,
	}
}

// GetSchemaContent returns the content of a schema
func (r *JSONContributionRegistry) GetSchemaContent(uri string) *string {
	r.mu.RLock()
	defer r.mu.RUnlock()

	schema := r.schemasById[uri]
	if schema == nil {
		return nil
	}
	
	// In a real implementation, this would serialize the schema to JSON
	// For now, return a placeholder
	content := "schema content"
	return &content
}

// HasSchemaContent checks if a schema exists
func (r *JSONContributionRegistry) HasSchemaContent(uri string) bool {
	r.mu.RLock()
	defer r.mu.RUnlock()

	return r.schemasById[uri] != nil
}

// GetSchemaAssociations returns all schema associations
func (r *JSONContributionRegistry) GetSchemaAssociations() map[string][]string {
	r.mu.RLock()
	defer r.mu.RUnlock()

	// Create a copy of the associations map
	associations := make(map[string][]string)
	for k, v := range r.schemaAssociations {
		associations[k] = make([]string, len(v))
		copy(associations[k], v)
	}

	return associations
}
