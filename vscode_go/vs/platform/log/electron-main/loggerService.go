/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package electronmain

import (
	"sync"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	lognode "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/node"
	instantiationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
)

// ILoggerMainService is the service identifier for LoggerMainService
var ILoggerMainService = instantiationcommon.CreateDecorator[ILoggerMainServiceInterface]("loggerMainService")

// ILoggerMainServiceInterface extends ILoggerService for electron-main environments
type ILoggerMainServiceInterface interface {
	logcommon.ILoggerService

	GetOnDidChangeLogLevelEvent(windowID int) basecommon.Event[interface{}] // LogLevel | [URI, LogLevel]
	GetOnDidChangeVisibilityEvent(windowID int) basecommon.Event[[]*basecommon.URI]
	GetOnDidChangeLoggersEvent(windowID int) basecommon.Event[logcommon.DidChangeLoggersEvent]

	CreateLoggerWithWindowID(idOrResource interface{}, options *logcommon.ILoggerOptions, windowID *int) logcommon.ILogger
	RegisterLoggerWithWindowID(resource logcommon.ILoggerResource, windowID *int)
	GetGlobalLoggers() []logcommon.ILoggerResource
	DeregisterLoggers(windowID int)
}

// LoggerMainService extends LoggerService for electron-main environments
type LoggerMainService struct {
	*lognode.LoggerService
	loggerResourcesByWindow *basecommon.ResourceMap[int]
	mu                      sync.RWMutex
}

// NewLoggerMainService creates a new LoggerMainService
func NewLoggerMainService() *LoggerMainService {
	return &LoggerMainService{
		LoggerService:           lognode.NewLoggerService(),
		loggerResourcesByWindow: basecommon.NewResourceMap[int](),
	}
}

// ServiceBrand returns the service brand
func (s *LoggerMainService) ServiceBrand() interface{} {
	return "loggerMainService"
}

// CreateLoggerWithWindowID creates a logger with optional window ID
func (s *LoggerMainService) CreateLoggerWithWindowID(idOrResource interface{}, options *logcommon.ILoggerOptions, windowID *int) logcommon.ILogger {
	if windowID != nil {
		resource := s.toResource(idOrResource)
		s.loggerResourcesByWindow.Set(resource, *windowID)
	}

	// Create logger using parent service
	var nodeOptions *lognode.LoggerOptions
	if options != nil {
		nodeOptions = &lognode.LoggerOptions{
			ID:                 options.ID,
			Name:               options.Name,
			DonotRotate:        options.DonotRotate,
			DonotUseFormatters: options.DonotUseFormatters,
		}
		if level, ok := options.LogLevel.(logcommon.LogLevel); ok {
			nodeOptions.LogLevel = &level
		}
		nodeOptions.Hidden = options.Hidden
		nodeOptions.When = options.When
		nodeOptions.ExtensionID = options.ExtensionID
	}

	logger := s.LoggerService.CreateLogger(idOrResource, nodeOptions)
	if logger == nil && windowID != nil {
		// If creation failed, remove from window mapping
		resource := s.toResource(idOrResource)
		s.loggerResourcesByWindow.Delete(resource)
	}

	return logger
}

// RegisterLoggerWithWindowID registers a logger with optional window ID
func (s *LoggerMainService) RegisterLoggerWithWindowID(resource logcommon.ILoggerResource, windowID *int) {
	if windowID != nil {
		s.loggerResourcesByWindow.Set(resource.Resource, *windowID)
	}

	// Convert to node logger resource format
	nodeResource := &lognode.LoggerResource{
		Resource:    resource.Resource,
		ID:          resource.ID,
		Name:        resource.Name,
		LogLevel:    resource.LogLevel,
		Hidden:      resource.Hidden,
		When:        resource.When,
		ExtensionID: resource.ExtensionID,
	}

	s.LoggerService.RegisterLogger(nodeResource)
}

// DeregisterLogger overrides parent to also remove from window mapping
func (s *LoggerMainService) DeregisterLogger(resource *basecommon.URI) {
	s.loggerResourcesByWindow.Delete(resource)
	s.LoggerService.DeregisterLogger(resource)
}

// GetGlobalLoggers returns loggers that are not associated with any window
func (s *LoggerMainService) GetGlobalLoggers() []logcommon.ILoggerResource {
	allLoggers := s.LoggerService.GetRegisteredLoggers()
	globalLoggers := make([]logcommon.ILoggerResource, 0)

	for _, logger := range allLoggers {
		if !s.loggerResourcesByWindow.Has(logger.Resource) {
			// Convert from node format to common format
			commonLogger := logcommon.ILoggerResource{
				Resource:    logger.Resource,
				ID:          logger.ID,
				Name:        logger.Name,
				LogLevel:    logger.LogLevel,
				Hidden:      logger.Hidden,
				When:        logger.When,
				ExtensionID: logger.ExtensionID,
			}
			globalLoggers = append(globalLoggers, commonLogger)
		}
	}

	return globalLoggers
}

// GetOnDidChangeLogLevelEvent returns filtered log level change events for a window
func (s *LoggerMainService) GetOnDidChangeLogLevelEvent(windowID int) basecommon.Event[interface{}] {
	// In Go, we can't easily represent the union type `LogLevel | [URI, LogLevel]`
	// as in TypeScript. We will return an Event[interface{}] and the consumer will have to type-assert.
	// The underlying event from node logger service only fires LogLevel, so we'll just filter based on that for now.
	// A more complete implementation might require a custom event type.
	filteredEvent := basecommon.EventFilter(s.OnDidChangeLogLevel(), func(arg logcommon.LogLevel) bool {
		// The TS implementation filters based on logger resource, but the node event only provides LogLevel.
		// For now, we pass all level changes through. A proper implementation would need to change the event signature.
		return true
	})
	return basecommon.EventMap(filteredEvent, func(level logcommon.LogLevel) interface{} {
		return level
	})
}

// GetOnDidChangeVisibilityEvent returns filtered visibility change events for a window
func (s *LoggerMainService) GetOnDidChangeVisibilityEvent(windowID int) basecommon.Event[[]*basecommon.URI] {
	return basecommon.EventFilter(s.OnDidChangeVisibility(), func(uris []*basecommon.URI) bool {
		if len(uris) > 0 {
			return s.isInterestedLoggerResource(uris[0], windowID)
		}
		return false
	})
}

// GetOnDidChangeLoggersEvent returns filtered logger change events for a window
func (s *LoggerMainService) GetOnDidChangeLoggersEvent(windowID int) basecommon.Event[logcommon.DidChangeLoggersEvent] {
	return basecommon.EventFilter(basecommon.EventMap(s.OnDidChangeLoggers(), func(e lognode.DidChangeLoggersEvent) logcommon.DidChangeLoggersEvent {
		added := make([]logcommon.ILoggerResource, 0)
		removed := make([]logcommon.ILoggerResource, 0)

		for _, logger := range e.Added {
			if s.isInterestedLoggerResource(logger.Resource, windowID) {
				added = append(added, toCommonLoggerResource(logger))
			}
		}

		for _, logger := range e.Removed {
			if s.isInterestedLoggerResource(logger.Resource, windowID) {
				removed = append(removed, toCommonLoggerResource(logger))
			}
		}

		return logcommon.DidChangeLoggersEvent{
			Added:   added,
			Removed: removed,
		}
	}), func(e logcommon.DidChangeLoggersEvent) bool {
		return len(e.Added) > 0 || len(e.Removed) > 0
	})
}

func toCommonLoggerResource(nodeLogger *lognode.LoggerResource) logcommon.ILoggerResource {
	return logcommon.ILoggerResource{
		Resource:    nodeLogger.Resource,
		ID:          nodeLogger.ID,
		Name:        nodeLogger.Name,
		LogLevel:    nodeLogger.LogLevel,
		Hidden:      nodeLogger.Hidden,
		When:        nodeLogger.When,
		ExtensionID: nodeLogger.ExtensionID,
	}
}

// DeregisterLoggers removes all loggers associated with a window
func (s *LoggerMainService) DeregisterLoggers(windowID int) {
	s.mu.Lock()
	defer s.mu.Unlock()

	resourcesToRemove := make([]*basecommon.URI, 0)

	// Collect resources to remove
	iterator := s.loggerResourcesByWindow.Iterator()
	for {
		uri, resourceWindowID, hasNext := iterator()
		if !hasNext {
			break
		}
		if resourceWindowID == windowID {
			resourcesToRemove = append(resourcesToRemove, uri)
		}
	}

	// Remove the resources
	for _, resource := range resourcesToRemove {
		s.DeregisterLogger(resource)
	}
}

// isInterestedLoggerResource checks if a logger resource is of interest for a window
func (s *LoggerMainService) isInterestedLoggerResource(resource *basecommon.URI, windowID int) bool {
	if loggerWindowID, exists := s.loggerResourcesByWindow.Get(resource); exists {
		return loggerWindowID == windowID
	}
	// If no window ID is associated, it's a global logger
	return true
}

// toResource converts an ID or URI to a URI
func (s *LoggerMainService) toResource(idOrResource interface{}) *basecommon.URI {
	switch v := idOrResource.(type) {
	case *basecommon.URI:
		return v
	case string:
		return basecommon.ParseURI(v)
	default:
		panic("idOrResource must be URI or string")
	}
}

// Dispose disposes the logger main service
func (s *LoggerMainService) Dispose() {
	s.loggerResourcesByWindow.Clear()
	s.LoggerService.Dispose()
}