/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"fmt"
	"sync"
)

// IRegistry defines the interface for a registry.
// The generic type parameter is not directly supported in Go in the same way as TypeScript,
// so we use interface{} and type assertions.
type IRegistry interface {
	// Add adds the extension functions and properties defined by data to the
	// platform. The provided id must be unique.
	Add(id string, data interface{})

	// Knows returns true iff there is an extension with the provided id.
	Knows(id string) bool

	// As returns the extension functions and properties defined by the specified key or nil.
	// The caller is expected to perform a type assertion on the returned value.
	As(id string) interface{}
}

type registryImpl struct {
	data map[string]interface{}
	mu   sync.RWMutex
}

func (r *registryImpl) Add(id string, data interface{}) {
	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.data[id]; exists {
		panic(fmt.Sprintf("There is already an extension with this id: %s", id))
	}
	r.data[id] = data
}

func (r *registryImpl) Knows(id string) bool {
	r.mu.RLock()
	defer r.mu.RUnlock()

	_, exists := r.data[id]
	return exists
}

func (r *registryImpl) As(id string) interface{} {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if data, exists := r.data[id]; exists {
		return data
	}
	return nil
}

// Registry is the global instance of the registry.
var Registry IRegistry = &registryImpl{
	data: make(map[string]interface{}),
}
