/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	nls "github.com/yudaprama/kawai-agent/vscode_go/vs/nls"
	configurationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/configuration/common"
	"github.com/yudaprama/kawai-agent/vscode_go/vs/platform/registry/common"
)

func init() {
	configurationRegistry := common.Registry.As(configurationcommon.Extensions.Configuration).(configurationcommon.IConfigurationRegistry)

	order := 15
	included := basecommon.IsWindows && !basecommon.IsWeb
	applicationScope := configurationcommon.ConfigurationScopeApplication
	deprecatedMessage := nls.Localize("deprecated", "This setting is deprecated, please use '{0}' instead.", "update.mode")
	minimumVersion := "1.67"

	configurationRegistry.RegisterConfiguration(&configurationcommon.IConfigurationNode{
		ID:    stringPtr("update"),
		Order: &order,
		Title: stringPtr(nls.Localize("updateConfigurationTitle", "Update")),
		Type:  "object",
		Properties: map[string]*configurationcommon.IConfigurationPropertySchema{
			"update.mode": {
				Type:    "string",
				Enum:    []string{"none", "manual", "start", "default"},
				Default: "default",
				Scope:   &applicationScope,
				Description: stringPtr(nls.Localize("updateMode", "Configure whether you receive automatic updates. Requires a restart after change. The updates are fetched from a Microsoft online service.")),
				Tags:    []string{"usesOnlineServices"},
				EnumDescriptions: []string{
					nls.Localize("none", "Disable updates."),
					nls.Localize("manual", "Disable automatic background update checks. Updates will be available if you manually check for updates."),
					nls.Localize("start", "Check for updates only on startup. Disable automatic background update checks."),
					nls.Localize("default", "Enable automatic update checks. Code will check for updates automatically and periodically."),
				},
				Policy: &configurationcommon.IPolicyInfo{
					Name:           "UpdateMode",
					MinimumVersion: &minimumVersion,
				},
			},
			"update.channel": {
				Type:               "string",
				Default:            "default",
				Scope:              &applicationScope,
				Description:        stringPtr(nls.Localize("updateChannel", "Configure whether you receive automatic updates. Requires a restart after change. The updates are fetched from a Microsoft online service.")),
				DeprecationMessage: &deprecatedMessage,
			},
			"update.enableWindowsBackgroundUpdates": {
				Type:        "boolean",
				Default:     true,
				Scope:       &applicationScope,
				Description: stringPtr(nls.Localize("enableWindowsBackgroundUpdates", "Enable to download and install new VS Code versions in the background on Windows.")),
				Included:    &included,
			},
			"update.showReleaseNotes": {
				Type:        "boolean",
				Default:     true,
				Scope:       &applicationScope,
				Description: stringPtr(nls.Localize("showReleaseNotes", "Show Release Notes after an update. The Release Notes are fetched from a Microsoft online service.")),
				Tags:        []string{"usesOnlineServices"},
			},
		},
	})
}

func stringPtr(s string) *string {
	return &s
}
