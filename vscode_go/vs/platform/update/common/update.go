/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	instantiationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
)

// IUpdate corresponds to the IUpdate interface in TypeScript.
type IUpdate struct {
	Version        string `json:"version"`
	ProductVersion string `json:"productVersion,omitempty"`
	Timestamp      int64  `json:"timestamp,omitempty"`
	URL            string `json:"url,omitempty"`
	SHA256Hash     string `json:"sha256hash,omitempty"`
}

// StateType corresponds to the StateType enum in TypeScript.
type StateType string

const (
	StateTypeUninitialized        StateType = "uninitialized"
	StateTypeIdle                 StateType = "idle"
	StateTypeDisabled             StateType = "disabled"
	StateTypeCheckingForUpdates   StateType = "checking for updates"
	StateTypeAvailableForDownload StateType = "available for download"
	StateTypeDownloading          StateType = "downloading"
	StateTypeDownloaded           StateType = "downloaded"
	StateTypeUpdating             StateType = "updating"
	StateTypeReady                StateType = "ready"
)

// UpdateType corresponds to the UpdateType enum in TypeScript.
type UpdateType int

const (
	UpdateTypeSetup UpdateType = iota
	UpdateTypeArchive
	UpdateTypeSnap
)

// DisablementReason corresponds to the DisablementReason enum in TypeScript.
type DisablementReason int

const (
	DisablementReasonNotBuilt DisablementReason = iota
	DisablementReasonDisabledByEnvironment
	DisablementReasonManuallyDisabled
	DisablementReasonMissingConfiguration
	DisablementReasonInvalidConfiguration
	DisablementReasonRunningAsAdmin
)

// State represents the state of the update service.
type State interface {
	GetType() StateType
}

// UninitializedState represents the uninitialized state.
type UninitializedState struct{}

func (s UninitializedState) GetType() StateType { return StateTypeUninitialized }

// DisabledState represents the disabled state.
type DisabledState struct {
	Reason DisablementReason
}

func (s DisabledState) GetType() StateType { return StateTypeDisabled }

// IdleState represents the idle state.
type IdleState struct {
	UpdateType UpdateType
	Error      string
}

func (s IdleState) GetType() StateType { return StateTypeIdle }

// CheckingForUpdatesState represents the checking for updates state.
type CheckingForUpdatesState struct {
	Explicit bool
}

func (s CheckingForUpdatesState) GetType() StateType { return StateTypeCheckingForUpdates }

// AvailableForDownloadState represents the available for download state.
type AvailableForDownloadState struct {
	Update IUpdate
}

func (s AvailableForDownloadState) GetType() StateType { return StateTypeAvailableForDownload }

// DownloadingState represents the downloading state.
type DownloadingState struct{}

func (s DownloadingState) GetType() StateType { return StateTypeDownloading }

// DownloadedState represents the downloaded state.
type DownloadedState struct {
	Update IUpdate
}

func (s DownloadedState) GetType() StateType { return StateTypeDownloaded }

// UpdatingState represents the updating state.
type UpdatingState struct {
	Update IUpdate
}

func (s UpdatingState) GetType() StateType { return StateTypeUpdating }

// ReadyState represents the ready state.
type ReadyState struct {
	Update IUpdate
}

func (s ReadyState) GetType() StateType { return StateTypeReady }

// IUpdateService is the main update service interface.
var IUpdateServiceID = instantiationcommon.CreateDecorator[IUpdateService]("updateService")

type IUpdateService interface {
	OnStateChange() basecommon.Event[State]
	GetState() State
	CheckForUpdates(explicit bool) error
	DownloadUpdate() error
	ApplyUpdate() error
	QuitAndInstall() error
	IsLatestVersion() (bool, error)
	_applySpecificUpdate(packagePath string) error
}
