/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	baseCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	environmentCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment/common"
	platformFilesCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	"github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/node/watcher"
	"github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/node/watcher/nodejs"
	platformLogCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
)

// Provider is an internal interface to be implemented by the concrete provider.
// This is a workaround for Go's lack of `protected abstract` methods.
type Provider interface {
	createUniversalWatcher(
		onChange func(changes []platformFilesCommon.IFileChange),
		onLogMessage func(msg platformFilesCommon.ILogMessage),
		verboseLogging bool,
	) *watcher.UniversalWatcherClient
	createNonRecursiveWatcher(
		onChange func(changes []platformFilesCommon.IFileChange),
		onLogMessage func(msg platformFilesCommon.ILogMessage),
		verboseLogging bool,
	) *nodejs.NodeJSWatcherClient
}

// IDiskFileSystemProviderOptions represents options for disk file system provider
type IDiskFileSystemProviderOptions struct {
	Watcher *struct {
		// Extra options for the recursive file watching
		Recursive *platformFilesCommon.IRecursiveWatcherOptions `json:"recursive"`
		// Forces all file watch requests to run through a single universal file watcher
		ForceUniversal bool `json:"forceUniversal"`
	} `json:"watcher"`
}

// AbstractDiskFileSystemProvider provides base functionality for disk file system providers
type AbstractDiskFileSystemProvider struct {
	*baseCommon.Disposable
	logService platformLogCommon.ILogService
	options    *IDiskFileSystemProviderOptions
	provider   Provider // The concrete provider that implements the watcher creation methods

	// Events
	onDidChangeFile *baseCommon.Emitter[[]platformFilesCommon.IFileChange]
	onDidWatchError *baseCommon.Emitter[string]

	// Universal watcher
	universalWatcher             *watcher.UniversalWatcherClient
	universalWatchRequests       []platformFilesCommon.IUniversalWatchRequest
	universalWatchRequestDelayer *baseCommon.ThrottledDelayer[interface{}]

	// Non-recursive watcher
	nonRecursiveWatcher             *nodejs.NodeJSWatcherClient
	nonRecursiveWatchRequests       []platformFilesCommon.INonRecursiveWatchRequest
	nonRecursiveWatchRequestDelayer *baseCommon.ThrottledDelayer[interface{}]

	mu sync.RWMutex
}

// NewAbstractDiskFileSystemProvider creates a new abstract disk file system provider
func NewAbstractDiskFileSystemProvider(
	logService platformLogCommon.ILogService,
	options *IDiskFileSystemProviderOptions,
) *AbstractDiskFileSystemProvider {
	p := &AbstractDiskFileSystemProvider{
		Disposable:                      baseCommon.NewDisposable(),
		logService:                      logService,
		options:                         options,
		onDidChangeFile:                 baseCommon.NewEmitter[[]platformFilesCommon.IFileChange](),
		onDidWatchError:                 baseCommon.NewEmitter[string](),
		universalWatchRequestDelayer:    baseCommon.NewThrottledDelayer[interface{}](0),
		nonRecursiveWatchRequestDelayer: baseCommon.NewThrottledDelayer[interface{}](0),
	}

	// Register disposables
	p.Register(p.onDidChangeFile)
	p.Register(p.onDidWatchError)
	p.Register(p.universalWatchRequestDelayer)
	p.Register(p.nonRecursiveWatchRequestDelayer)

	return p
}

// SetProvider sets the concrete provider.
func (p *AbstractDiskFileSystemProvider) SetProvider(provider Provider) {
	p.provider = provider
}

// OnDidChangeFile returns the file change event
func (p *AbstractDiskFileSystemProvider) OnDidChangeFile() baseCommon.Event[[]platformFilesCommon.IFileChange] {
	return p.onDidChangeFile.Event()
}

// OnDidWatchError returns the watch error event
func (p *AbstractDiskFileSystemProvider) OnDidWatchError() baseCommon.Event[string] {
	return p.onDidWatchError.Event()
}

// Watch watches a resource for changes
func (p *AbstractDiskFileSystemProvider) Watch(resource *baseCommon.URI, opts platformFilesCommon.IWatchOptions) baseCommon.IDisposable {
	if opts.Recursive || (p.options != nil && p.options.Watcher != nil && p.options.Watcher.ForceUniversal) {
		return p.watchUniversal(resource, opts)
	}
	return p.watchNonRecursive(resource, opts)
}

// getRefreshWatchersDelay returns the delay for refreshing watchers based on count
func (p *AbstractDiskFileSystemProvider) getRefreshWatchersDelay(count int) time.Duration {
	if count > 200 {
		// If there are many requests to refresh, start to throttle
		return 500 * time.Millisecond
	}
	// By default, use a short delay
	return 0
}

// watchUniversal watches using universal watcher
func (p *AbstractDiskFileSystemProvider) watchUniversal(resource *baseCommon.URI, opts platformFilesCommon.IWatchOptions) baseCommon.IDisposable {
	p.mu.Lock()
	defer p.mu.Unlock()

	request := p.toWatchRequest(resource, opts)

	// Add to requests
	p.universalWatchRequests = append(p.universalWatchRequests, request)

	// Trigger update
	p.refreshUniversalWatchers()

	return baseCommon.ToDisposable(func() {
		p.mu.Lock()
		defer p.mu.Unlock()

		// Remove from list
		for i, req := range p.universalWatchRequests {
			if req.GetPath() == request.GetPath() {
				p.universalWatchRequests = append(p.universalWatchRequests[:i], p.universalWatchRequests[i+1:]...)
				break
			}
		}

		// Trigger update
		p.refreshUniversalWatchers()
	})
}

// stringSliceToInterfaceSlice converts a slice of strings to a slice of interfaces
func stringSliceToInterfaceSlice(input []string) []interface{} {
	if input == nil {
		return nil
	}
	output := make([]interface{}, len(input))
	for i, v := range input {
		output[i] = v
	}
	return output
}

// toWatchRequest converts to watch request
func (p *AbstractDiskFileSystemProvider) toWatchRequest(resource *baseCommon.URI, opts platformFilesCommon.IWatchOptions) platformFilesCommon.IUniversalWatchRequest {
	path := p.toWatchPath(resource)

	if opts.Recursive {
		request := &platformFilesCommon.IRecursiveWatchRequest{
			IWatchRequest: platformFilesCommon.IWatchRequest{
				Path:          path,
				Recursive:     opts.Recursive,
				Excludes:      opts.Excludes,
				Includes:      stringSliceToInterfaceSlice(opts.Includes),
				CorrelationId: opts.CorrelationId,
				Filter:        opts.Filter,
			},
		}

		// Adjust for polling
		if p.options != nil && p.options.Watcher != nil && p.options.Watcher.Recursive != nil {
			usePolling := p.options.Watcher.Recursive.UsePolling
			if usePolling != nil {
				if boolVal, ok := usePolling.(bool); ok && boolVal {
					pollingInterval := 5000
					if p.options.Watcher.Recursive.PollingInterval != nil {
						pollingInterval = *p.options.Watcher.Recursive.PollingInterval
					}
					request.PollingInterval = &pollingInterval
				} else if arrayVal, ok := usePolling.([]string); ok {
					for _, pathPattern := range arrayVal {
						if pathPattern == path {
							pollingInterval := 5000
							if p.options.Watcher.Recursive.PollingInterval != nil {
								pollingInterval = *p.options.Watcher.Recursive.PollingInterval
							}
							request.PollingInterval = &pollingInterval
							break
						}
					}
				}
			}
		}

		return request
	}

	return &platformFilesCommon.INonRecursiveWatchRequest{
		IWatchRequest: platformFilesCommon.IWatchRequest{
			Path:          path,
			Recursive:     false,
			Excludes:      opts.Excludes,
			Includes:      stringSliceToInterfaceSlice(opts.Includes),
			CorrelationId: opts.CorrelationId,
			Filter:        opts.Filter,
		},
	}
}

// refreshUniversalWatchers refreshes universal watchers
func (p *AbstractDiskFileSystemProvider) refreshUniversalWatchers() {
	delay := p.getRefreshWatchersDelay(len(p.universalWatchRequests))
	p.universalWatchRequestDelayer.Trigger(func() <-chan interface{} {
		result := make(chan interface{}, 1)
		go func() {
			defer close(result)
			p.doRefreshUniversalWatchers()
			result <- nil
		}()
		return result
	}, delay)
}

// doRefreshUniversalWatchers performs the actual refresh of universal watchers
func (p *AbstractDiskFileSystemProvider) doRefreshUniversalWatchers() {
	// Create watcher if this is the first time
	if p.universalWatcher == nil {
		p.universalWatcher = p.provider.createUniversalWatcher(
			func(changes []platformFilesCommon.IFileChange) {
				p.onDidChangeFile.Fire(platformFilesCommon.ReviveFileChanges(changes))
			},
			func(msg platformFilesCommon.ILogMessage) {
				p.onWatcherLogMessage(msg)
			},
			p.logService.GetLevel() == platformLogCommon.LogLevelTrace,
		)

		// Register for disposal
		p.Register(p.universalWatcher)

		// Note: Go version doesn't have dynamic log level changes yet
		// TODO: Implement log level change events when available
	}

	// Ask to watch the provided paths
	if p.universalWatcher != nil {
		p.universalWatcher.Watch(p.universalWatchRequests)
	}
}

// watchNonRecursive watches using non-recursive watcher
func (p *AbstractDiskFileSystemProvider) watchNonRecursive(resource *baseCommon.URI, opts platformFilesCommon.IWatchOptions) baseCommon.IDisposable {
	p.mu.Lock()
	defer p.mu.Unlock()

	request := &platformFilesCommon.INonRecursiveWatchRequest{
		IWatchRequest: platformFilesCommon.IWatchRequest{
			Path:          p.toWatchPath(resource),
			Recursive:     false,
			Excludes:      opts.Excludes,
			Includes:      stringSliceToInterfaceSlice(opts.Includes),
			CorrelationId: opts.CorrelationId,
			Filter:        opts.Filter,
		},
	}

	// Add to requests
	p.nonRecursiveWatchRequests = append(p.nonRecursiveWatchRequests, *request)

	// Trigger update
	p.refreshNonRecursiveWatchers()

	return baseCommon.ToDisposable(func() {
		p.mu.Lock()
		defer p.mu.Unlock()

		// Remove from list
		for i, req := range p.nonRecursiveWatchRequests {
			if req.Path == request.Path {
				p.nonRecursiveWatchRequests = append(p.nonRecursiveWatchRequests[:i], p.nonRecursiveWatchRequests[i+1:]...)
				break
			}
		}

		// Trigger update
		p.refreshNonRecursiveWatchers()
	})
}

// refreshNonRecursiveWatchers refreshes non-recursive watchers
func (p *AbstractDiskFileSystemProvider) refreshNonRecursiveWatchers() {
	delay := p.getRefreshWatchersDelay(len(p.nonRecursiveWatchRequests))
	p.nonRecursiveWatchRequestDelayer.Trigger(func() <-chan interface{} {
		result := make(chan interface{}, 1)
		go func() {
			defer close(result)
			p.doRefreshNonRecursiveWatchers()
			result <- nil
		}()
		return result
	}, delay)
}

// doRefreshNonRecursiveWatchers performs the actual refresh of non-recursive watchers
func (p *AbstractDiskFileSystemProvider) doRefreshNonRecursiveWatchers() {
	// Create watcher if this is the first time
	if p.nonRecursiveWatcher == nil {
		p.nonRecursiveWatcher = p.provider.createNonRecursiveWatcher(
			func(changes []platformFilesCommon.IFileChange) {
				p.onDidChangeFile.Fire(platformFilesCommon.ReviveFileChanges(changes))
			},
			func(msg platformFilesCommon.ILogMessage) {
				p.onWatcherLogMessage(msg)
			},
			p.logService.GetLevel() == platformLogCommon.LogLevelTrace,
		)

		// Register for disposal
		p.Register(p.nonRecursiveWatcher)

		// Note: Go version doesn't have dynamic log level changes yet
		// TODO: Implement log level change events when available
	}

	// Ask to watch the provided paths
	if p.nonRecursiveWatcher != nil {
		p.nonRecursiveWatcher.Watch(p.nonRecursiveWatchRequests)
	}
}

// onWatcherLogMessage handles watcher log messages
func (p *AbstractDiskFileSystemProvider) onWatcherLogMessage(msg platformFilesCommon.ILogMessage) {
	if msg.Type == "error" {
		p.onDidWatchError.Fire(msg.Message)
	}
	p.logWatcherMessage(msg)
}

// logWatcherMessage logs watcher messages
func (p *AbstractDiskFileSystemProvider) logWatcherMessage(msg platformFilesCommon.ILogMessage) {
	switch msg.Type {
	case "trace":
		p.logService.Trace(msg.Message)
	case "debug":
		p.logService.Debug(msg.Message)
	case "info":
		p.logService.Info(msg.Message)
	case "warn":
		p.logService.Warn(msg.Message)
	case "error":
		p.logService.Error(msg.Message)
	}
}

// ToFilePath converts URI to file path
func (p *AbstractDiskFileSystemProvider) ToFilePath(resource *baseCommon.URI) string {
	return baseCommon.Normalize(resource.FSPath())
}

// toWatchPath converts URI to watch path
func (p *AbstractDiskFileSystemProvider) toWatchPath(resource *baseCommon.URI) string {
	filePath := p.ToFilePath(resource)
	// Ensure to have any trailing path separators removed
	return baseCommon.RemoveTrailingPathSeparator(filePath)
}

// Barrier provides a synchronization barrier for resource locking
type Barrier struct {
	promise *baseCommon.DeferredPromise[interface{}]
	mu      sync.RWMutex
	opened  bool
}

// NewBarrier creates a new barrier
func NewBarrier() *Barrier {
	return &Barrier{
		promise: baseCommon.NewDeferredPromise[interface{}](),
	}
}

// Wait waits for the barrier to be opened
func (b *Barrier) Wait() <-chan interface{} {
	b.mu.RLock()
	defer b.mu.RUnlock()

	if b.opened {
		// Return a channel that's already ready
		result := make(chan interface{}, 1)
		result <- nil
		close(result)
		return result
	}

	return b.promise.Promise()
}

// Open opens the barrier, allowing all waiting goroutines to proceed
func (b *Barrier) Open() {
	b.mu.Lock()
	defer b.mu.Unlock()

	if !b.opened {
		b.opened = true
		b.promise.Complete(nil)
	}
}

// DiskFileSystemProvider implements a complete file system provider for local disk access
type DiskFileSystemProvider struct {
	*AbstractDiskFileSystemProvider

	// Environment service for context
	environmentService environmentCommon.INativeEnvironmentService

	// File locking and validation
	fileLockMap   map[string]*sync.RWMutex
	fileLockMutex sync.RWMutex

	// Resource locking for atomic operations
	resourceLocks *baseCommon.ResourceMap[*Barrier]

	// File handle management
	mapHandleToPos  map[int]int64
	mapHandleToLock map[int]baseCommon.IDisposable
	writeHandles    map[int]*baseCommon.URI
	handleMutex     sync.RWMutex
	nextHandle      int

	// Capabilities
	capabilities platformFilesCommon.FileSystemProviderCapabilities

	// Events
	onDidChangeCapabilities *baseCommon.Emitter[struct{}]

	// Trash/recycle bin support
	recycleSupport bool

	// Tracing
	traceResourceLocks bool
}

// NewDiskFileSystemProvider creates a new disk file system provider
func NewDiskFileSystemProvider(
	logService platformLogCommon.ILogService,
	environmentService environmentCommon.INativeEnvironmentService,
	options *IDiskFileSystemProviderOptions,
) *DiskFileSystemProvider {
	provider := &DiskFileSystemProvider{
		AbstractDiskFileSystemProvider: NewAbstractDiskFileSystemProvider(logService, options),
		environmentService:             environmentService,
		fileLockMap:                    make(map[string]*sync.RWMutex),
		resourceLocks:                  baseCommon.NewResourceMap[*Barrier](),
		mapHandleToPos:                 make(map[int]int64),
		mapHandleToLock:                make(map[int]baseCommon.IDisposable),
		writeHandles:                   make(map[int]*baseCommon.URI),
		nextHandle:                     1,
		onDidChangeCapabilities:        baseCommon.NewEmitter[struct{}](),
		traceResourceLocks:             false, // TODO: add configuration
	}

	// Set the provider on the abstract base
	provider.AbstractDiskFileSystemProvider.SetProvider(provider)

	// Set capabilities
	provider.setCapabilities()

	// Setup recycle support
	provider.setupRecycleSupport()

	// Register disposables
	provider.Register(provider.onDidChangeCapabilities)

	return provider
}

// setCapabilities sets the provider capabilities
func (p *DiskFileSystemProvider) setCapabilities() {
	p.capabilities = platformFilesCommon.FileSystemProviderCapabilitiesFileReadWrite |
		platformFilesCommon.FileSystemProviderCapabilitiesFileOpenReadWriteClose |
		platformFilesCommon.FileSystemProviderCapabilitiesFileReadStream |
		platformFilesCommon.FileSystemProviderCapabilitiesFileFolderCopy |
		platformFilesCommon.FileSystemProviderCapabilitiesPathCaseSensitive |
		platformFilesCommon.FileSystemProviderCapabilitiesReadonly

	// Add atomic capabilities if supported
	if p.supportsAtomic() {
		p.capabilities |= platformFilesCommon.FileSystemProviderCapabilitiesFileAtomicRead |
			platformFilesCommon.FileSystemProviderCapabilitiesFileAtomicWrite |
			platformFilesCommon.FileSystemProviderCapabilitiesFileAtomicDelete
	}

	// Add trash capability if supported
	if p.recycleSupport {
		p.capabilities |= platformFilesCommon.FileSystemProviderCapabilitiesTrash
	}
}

// setupRecycleSupport sets up recycle bin support
func (p *DiskFileSystemProvider) setupRecycleSupport() {
	// For now, default to true, can be disabled
	p.recycleSupport = true
}

// GetCapabilities returns the provider capabilities
func (p *DiskFileSystemProvider) GetCapabilities() platformFilesCommon.FileSystemProviderCapabilities {
	return p.capabilities
}

// OnDidChangeCapabilities returns the capability change event
func (p *DiskFileSystemProvider) OnDidChangeCapabilities() baseCommon.Event[struct{}] {
	return p.onDidChangeCapabilities.Event()
}

// supportsAtomic checks if the file system supports atomic operations
func (p *DiskFileSystemProvider) supportsAtomic() bool {
	// On Windows, atomic operations are supported via MoveFileEx
	// On POSIX systems, atomic operations are supported via rename
	return true
}

// Realpath resolves symbolic links and returns the canonical path
func (p *DiskFileSystemProvider) Realpath(resource *baseCommon.URI) (string, error) {
	filePath := p.ToFilePath(resource)

	realPath, err := filepath.EvalSymlinks(filePath)
	if err != nil {
		return "", p.toFileSystemProviderError(err)
	}

	return realPath, nil
}

// createUniversalWatcher creates a universal watcher implementation
func (p *DiskFileSystemProvider) createUniversalWatcher(
	onChange func(changes []platformFilesCommon.IFileChange),
	onLogMessage func(msg platformFilesCommon.ILogMessage),
	verboseLogging bool,
) *watcher.UniversalWatcherClient {
	return watcher.NewUniversalWatcherClient(onChange, onLogMessage, verboseLogging)
}

// createNonRecursiveWatcher creates a non-recursive watcher implementation
func (p *DiskFileSystemProvider) createNonRecursiveWatcher(
	onChange func(changes []platformFilesCommon.IFileChange),
	onLogMessage func(msg platformFilesCommon.ILogMessage),
	verboseLogging bool,
) *nodejs.NodeJSWatcherClient {
	return nodejs.NewNodeJSWatcherClient(onChange, onLogMessage, verboseLogging)
}

// getFileLock gets or creates a file lock for a path
func (p *DiskFileSystemProvider) getFileLock(filePath string) *sync.RWMutex {
	p.fileLockMutex.Lock()
	defer p.fileLockMutex.Unlock()

	if lock, exists := p.fileLockMap[filePath]; exists {
		return lock
	}

	lock := &sync.RWMutex{}
	p.fileLockMap[filePath] = lock
	return lock
}

// Dispose cleans up the provider
func (p *DiskFileSystemProvider) Dispose() {
	p.AbstractDiskFileSystemProvider.Dispose()

	p.fileLockMutex.Lock()
	p.fileLockMap = make(map[string]*sync.RWMutex)
	p.fileLockMutex.Unlock()
}

// #region File Metadata

type symbolicLink struct {
	dangling bool
}

// Stat returns file statistics for a given resource.
func (p *DiskFileSystemProvider) Stat(resource *baseCommon.URI) (*platformFilesCommon.IStat, error) {
	filePath := p.ToFilePath(resource)

	lstatInfo, err := os.Lstat(filePath)
	if err != nil {
		return nil, p.toFileSystemProviderError(err)
	}

	var statInfo os.FileInfo = lstatInfo
	var sl *symbolicLink
	if lstatInfo.Mode()&os.ModeSymlink != 0 {
		sl = &symbolicLink{}
		statInfo, err = os.Stat(filePath) // This will resolve the symlink
		if err != nil {
			if os.IsNotExist(err) {
				sl.dangling = true // The symlink target does not exist
			} else {
				return nil, p.toFileSystemProviderError(err) // A real error occurred
			}
		}
	}

	resolvedInfo := statInfo
	if sl != nil && sl.dangling {
		resolvedInfo = lstatInfo
	}

	permissions := p.getFilePermissions(lstatInfo)
	return &platformFilesCommon.IStat{
		Type:        p.toType(resolvedInfo, sl),
		Ctime:       p.getCreationTime(lstatInfo),
		Mtime:       lstatInfo.ModTime().UnixMilli(),
		Size:        lstatInfo.Size(),
		Permissions: &permissions,
	}, nil
}

func (p *DiskFileSystemProvider) toType(info os.FileInfo, sl *symbolicLink) platformFilesCommon.FileType {
	var fileType platformFilesCommon.FileType
	if sl != nil {
		fileType |= platformFilesCommon.FileTypeSymbolicLink
	}

	if info.IsDir() {
		fileType |= platformFilesCommon.FileTypeDirectory
	} else {
		fileType |= platformFilesCommon.FileTypeFile
	}

	return fileType
}

func (p *DiskFileSystemProvider) getCreationTime(info os.FileInfo) int64 {
	return info.ModTime().UnixMilli()
}

func (p *DiskFileSystemProvider) getFilePermissions(info os.FileInfo) platformFilesCommon.FilePermission {
	var permissions platformFilesCommon.FilePermission
	if info.Mode()&0200 == 0 { // Check for owner write permission
		permissions |= platformFilesCommon.FilePermissionReadonly
	}
	return permissions
}

// #endregion

// #region File I/O

func (p *DiskFileSystemProvider) ReadFile(resource *baseCommon.URI) ([]byte, error) {
	filePath := p.ToFilePath(resource)
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, p.toFileSystemProviderError(err)
	}
	return data, nil
}

func (p *DiskFileSystemProvider) WriteFile(resource *baseCommon.URI, content []byte, opts platformFilesCommon.IFileWriteOptions) error {
	filePath := p.ToFilePath(resource)
	if err := os.WriteFile(filePath, content, 0644); err != nil {
		return p.toFileSystemProviderError(err)
	}
	return nil
}

func (p *DiskFileSystemProvider) Mkdir(resource *baseCommon.URI) error {
	filePath := p.ToFilePath(resource)
	// The permission 0755 is commonly used for directories.
	// It grants read, write, and execute permissions to the owner,
	// and read and execute permissions to the group and others.
	if err := os.Mkdir(filePath, 0755); err != nil {
		return p.toFileSystemProviderError(err)
	}
	return nil
}

// DirEntry represents a directory entry.

type DirEntry struct {
	Name string
	Type platformFilesCommon.FileType
}

func (p *DiskFileSystemProvider) ReadDir(resource *baseCommon.URI) ([]DirEntry, error) {
	filePath := p.ToFilePath(resource)
	entries, err := os.ReadDir(filePath)
	if err != nil {
		return nil, p.toFileSystemProviderError(err)
	}

	results := make([]DirEntry, 0, len(entries))
	for _, entry := range entries {
		info, err := entry.Info()
		if err != nil {
			// In case of an error, we skip the entry, similar to the TS implementation.
			continue
		}

		var fileType platformFilesCommon.FileType
		if entry.Type()&os.ModeSymlink != 0 {
			// If it's a symlink, we need to resolve it to get the correct file type.
			resolvedStat, err := p.Stat(baseCommon.NewURI("file", "", filepath.Join(filePath, entry.Name()), "", ""))
			if err != nil {
				// If resolving fails, we treat it as an unknown file type.
				fileType = platformFilesCommon.FileTypeUnknown
			} else {
				fileType = resolvedStat.Type
			}
		} else {
			fileType = p.toType(info, nil)
		}

		results = append(results, DirEntry{
			Name: entry.Name(),
			Type: fileType,
		})
	}

	return results, nil
}

func (p *DiskFileSystemProvider) Delete(resource *baseCommon.URI, opts platformFilesCommon.IFileDeleteOptions) error {
	filePath := p.ToFilePath(resource)

	var err error
	if opts.Recursive {
		err = os.RemoveAll(filePath)
	} else {
		err = os.Remove(filePath)
	}

	if err != nil {
		return p.toFileSystemProviderError(err)
	}

	return nil
}

func (p *DiskFileSystemProvider) Rename(from *baseCommon.URI, to *baseCommon.URI, opts platformFilesCommon.IFileOverwriteOptions) error {
	fromFilePath := p.ToFilePath(from)
	toFilePath := p.ToFilePath(to)

	// If the destination exists and overwrite is not specified, we should fail.
	// If overwrite is specified, we need to delete the destination before renaming.
	if _, err := os.Stat(toFilePath); err == nil {
		if !opts.Overwrite {
			return p.toFileSystemProviderError(fmt.Errorf("file already exists at destination: %s", toFilePath))
		}
		if err := os.RemoveAll(toFilePath); err != nil {
			return p.toFileSystemProviderError(err)
		}
	} else if !os.IsNotExist(err) {
		// Any other error from stat should be reported
		return p.toFileSystemProviderError(err)
	}

	if err := os.Rename(fromFilePath, toFilePath); err != nil {
		return p.toFileSystemProviderError(err)
	}

	return nil
}

func (p *DiskFileSystemProvider) Copy(from *baseCommon.URI, to *baseCommon.URI, opts platformFilesCommon.IFileOverwriteOptions) error {
	// Placeholder implementation
	return nil
}

// #endregion

// #region Helpers

func (p *DiskFileSystemProvider) toFileSystemProviderError(err error) error {
	if err == nil {
		return nil
	}

	var code platformFilesCommon.FileSystemProviderErrorCode
	switch {
	case os.IsNotExist(err):
		code = platformFilesCommon.FileSystemProviderErrorCodeFileNotFound
	case os.IsExist(err):
		code = platformFilesCommon.FileSystemProviderErrorCodeFileExists
	case os.IsPermission(err):
		code = platformFilesCommon.FileSystemProviderErrorCodeNoPermissions
	default:
		code = platformFilesCommon.FileSystemProviderErrorCodeUnknown
	}

	return platformFilesCommon.NewFileSystemProviderError(err.Error(), code)
}

// #endregion

// createResourceLock creates a resource lock for atomic operations
func (p *DiskFileSystemProvider) createResourceLock(resource *baseCommon.URI) (baseCommon.IDisposable, error) {
	filePath := p.ToFilePath(resource)
	if p.traceResourceLocks {
		p.logService.Trace("[Disk FileSystemProvider]: createResourceLock() - request to acquire resource lock (" + filePath + ")")
	}

	// Await pending locks for resource. It is possible for a new lock being
	// added right after opening, so we have to loop over locks until no lock
	// remains.
	for {
		lock, exists := p.resourceLocks.Get(resource)
		if !exists {
			break
		}

		if p.traceResourceLocks {
			p.logService.Trace("[Disk FileSystemProvider]: createResourceLock() - waiting for resource lock to be released (" + filePath + ")")
		}

		<-lock.Wait()
	}

	// Store new lock
	newLock := NewBarrier()
	p.resourceLocks.Set(resource, newLock)

	if p.traceResourceLocks {
		p.logService.Trace("[Disk FileSystemProvider]: createResourceLock() - new resource lock created (" + filePath + ")")
	}

	return baseCommon.ToDisposable(func() {
		if p.traceResourceLocks {
			p.logService.Trace("[Disk FileSystemProvider]: createResourceLock() - resource lock dispose() (" + filePath + ")")
		}

		// Delete lock if it is still ours
		if lock, exists := p.resourceLocks.Get(resource); exists && lock == newLock {
			if p.traceResourceLocks {
				p.logService.Trace("[Disk FileSystemProvider]: createResourceLock() - resource lock removed from resource-lock map (" + filePath + ")")
			}
			p.resourceLocks.Delete(resource)
		}

		// Open lock
		if p.traceResourceLocks {
			p.logService.Trace("[Disk FileSystemProvider]: createResourceLock() - resource lock barrier open() (" + filePath + ")")
		}
		newLock.Open()
	}), nil
}

// ReadFileAtomic reads a file atomically
func (p *DiskFileSystemProvider) ReadFileAtomic(resource *baseCommon.URI, opts *platformFilesCommon.IFileAtomicReadOptions) ([]byte, error) {
	var lock baseCommon.IDisposable
	var err error

	if opts != nil && opts.Atomic {
		if p.traceResourceLocks {
			p.logService.Trace("[Disk FileSystemProvider]: atomic read operation started (" + p.ToFilePath(resource) + ")")
		}

		// When the read should be atomic, make sure
		// to await any pending locks for the resource
		// and lock for the duration of the read.
		lock, err = p.createResourceLock(resource)
		if err != nil {
			return nil, err
		}
		defer lock.Dispose()
	}

	return p.ReadFile(resource)
}

// WriteFileAtomic writes a file atomically
func (p *DiskFileSystemProvider) WriteFileAtomic(resource *baseCommon.URI, content []byte, opts platformFilesCommon.IFileWriteOptions) error {
	canWriteAtomic, err := p.canWriteFileAtomic(resource)
	if err != nil {
		return err
	}

	if canWriteAtomic && opts.Atomic != nil {
		if atomicOpts, ok := opts.Atomic.(platformFilesCommon.IFileAtomicOptions); ok && atomicOpts.Postfix != "" {
			resourceDir := baseCommon.NewURI("file", "", filepath.Dir(p.ToFilePath(resource)), "", "")
			resourceBase := filepath.Base(p.ToFilePath(resource))
			tempResource := baseCommon.JoinPath(resourceDir, resourceBase+atomicOpts.Postfix)
			return p.doWriteFileAtomic(resource, tempResource, content, opts)
		}
	}

	return p.WriteFile(resource, content, opts)
}

// canWriteFileAtomic checks if a file can be written atomically
func (p *DiskFileSystemProvider) canWriteFileAtomic(resource *baseCommon.URI) (bool, error) {
	filePath := p.ToFilePath(resource)
	info, err := os.Lstat(filePath)
	if err != nil {
		// If file doesn't exist, we can write atomically
		if os.IsNotExist(err) {
			return true, nil
		}
		return false, err
	}

	// atomic writes are unsupported for symbolic links because
	// we need to ensure that the `rename` operation is atomic
	// and that only works if the link is on the same disk.
	if info.Mode()&os.ModeSymlink != 0 {
		return false, nil
	}

	return true, nil
}

// doWriteFileAtomic performs atomic write using temporary file
func (p *DiskFileSystemProvider) doWriteFileAtomic(resource *baseCommon.URI, tempResource *baseCommon.URI, content []byte, opts platformFilesCommon.IFileWriteOptions) error {
	// Create locks for all resources involved
	locks := baseCommon.NewDisposableStore()
	defer locks.Dispose()

	// Lock the main resource
	lock1, err := p.createResourceLock(resource)
	if err != nil {
		return err
	}
	locks.Add(lock1)

	// Lock the temporary resource
	lock2, err := p.createResourceLock(tempResource)
	if err != nil {
		return err
	}
	locks.Add(lock2)

	// Write to temp resource first
	err = p.WriteFile(tempResource, content, opts)
	if err != nil {
		return err
	}

	// Rename over existing to ensure atomic replace
	err = p.Rename(tempResource, resource, platformFilesCommon.IFileOverwriteOptions{Overwrite: true})
	if err != nil {
		// Cleanup in case of rename error
		p.Delete(tempResource, platformFilesCommon.IFileDeleteOptions{
			Recursive: false,
			UseTrash:  false,
		})
		return err
	}

	return nil
}

// normalizePos normalizes file position for handle operations
func (p *DiskFileSystemProvider) normalizePos(fd int, pos int64) *int64 {
	p.handleMutex.RLock()
	defer p.handleMutex.RUnlock()

	if pos == -1 {
		// Use current position
		if currentPos, exists := p.mapHandleToPos[fd]; exists {
			return &currentPos
		}
		return nil
	}

	return &pos
}

// updatePos updates the position for a file handle
func (p *DiskFileSystemProvider) updatePos(fd int, pos *int64, bytesLength int64) {
	p.handleMutex.Lock()
	defer p.handleMutex.Unlock()

	if pos != nil {
		p.mapHandleToPos[fd] = *pos + bytesLength
	}
}
