/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package electronmain

import (
	"os"
	"sync"
	"time"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	"github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment/electron-main"
	instantiationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	statenode "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/state/node"
	windowelectronmain "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/window/electron-main"
	workspacecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/workspace/common"
)

const quitAndRestartKey = "lifecycle.quitAndRestart"

// LifecycleMainService implements ILifecycleMainService
type LifecycleMainService struct {
	onBeforeShutdown            *basecommon.Emitter[*BeforeShutdownEvent]
	onWillShutdown              *basecommon.Emitter[*ShutdownEvent]
	onWillLoadWindow            *basecommon.Emitter[*WindowLoadEvent]
	onBeforeCloseWindow         *basecommon.Emitter[windowelectronmain.ICodeWindow]

	quitRequested              bool
	wasRestarted               bool
	phase                      LifecycleMainPhase

	windowToCloseRequest        map[int]struct{}
	oneTimeListenerTokenGenerator int
	windowCounter               int
	windows                     map[int]windowelectronmain.ICodeWindow

	pendingQuitPromise      chan bool
	pendingQuitPromiseResolve func(bool)

	pendingWillShutdownPromise chan struct{}
	pendingWhenAllWindowsClosedPromise chan struct{}

	mapWindowIdToPendingUnload map[int]chan bool

	phaseWhen map[LifecycleMainPhase]*basecommon.Barrier

	relaunchHandler IRelaunchHandler

	logService             logcommon.ILogService
	stateService           statenode.IStateService
	environmentMainService electronmain.IEnvironmentMainServiceInterface

	mu sync.Mutex
}

// NewLifecycleMainService creates a new LifecycleMainService
func NewLifecycleMainService(
	logService logcommon.ILogService,
	stateService statenode.IStateService,
	environmentMainService electronmain.IEnvironmentMainServiceInterface,
) *LifecycleMainService {
	s := &LifecycleMainService{
		onBeforeShutdown:            basecommon.NewEmitter[*BeforeShutdownEvent](),
		onWillShutdown:              basecommon.NewEmitter[*ShutdownEvent](),
		onWillLoadWindow:            basecommon.NewEmitter[*WindowLoadEvent](),
		onBeforeCloseWindow:         basecommon.NewEmitter[windowelectronmain.ICodeWindow](),
		phase:                       LifecycleMainPhaseStarting,
		windowToCloseRequest:        make(map[int]struct{}),
		mapWindowIdToPendingUnload: make(map[int]chan bool),
		windows:                     make(map[int]windowelectronmain.ICodeWindow),
		phaseWhen:                   make(map[LifecycleMainPhase]*basecommon.Barrier),
		logService:                  logService,
		stateService:                stateService,
		environmentMainService:      environmentMainService,
	}

	s.resolveRestarted()
	go func() {
		<-s.When(LifecycleMainPhaseReady)
		// s.registerListeners() // TODO: Implement this
	}()

	return s
}

func (s *LifecycleMainService) resolveRestarted() {
	item := s.stateService.GetItem(quitAndRestartKey, false)
	s.wasRestarted = item.(bool)

	if s.wasRestarted {
		s.stateService.RemoveItem(quitAndRestartKey)
	}
}

// WasRestarted returns true if the program was restarted
func (s *LifecycleMainService) WasRestarted() bool {
	return s.wasRestarted
}

// QuitRequested returns true if a quit has been requested
func (s *LifecycleMainService) QuitRequested() bool {
	return s.quitRequested
}

// Phase returns the current lifecycle phase
func (s *LifecycleMainService) Phase() LifecycleMainPhase {
	return s.phase
}

// SetPhase sets the current lifecycle phase
func (s *LifecycleMainService) SetPhase(phase LifecycleMainPhase) {
	s.mu.Lock()
	defer s.mu.Unlock()

	if phase < s.phase {
		panic("Lifecycle cannot go backwards")
	}

	if s.phase == phase {
		return
	}

	s.tracef("lifecycle (main): phase changed (value: %d)", phase)

	s.phase = phase

	if barrier, ok := s.phaseWhen[s.phase]; ok {
		barrier.Open()
		delete(s.phaseWhen, s.phase)
	}
}

// When returns a channel that resolves when a certain lifecycle phase has started
func (s *LifecycleMainService) When(phase LifecycleMainPhase) <-chan struct{} {
	if phase <= s.phase {
		closedCh := make(chan struct{})
		close(closedCh)
		return closedCh
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	barrier, ok := s.phaseWhen[phase]
	if !ok {
		barrier = basecommon.NewBarrier()
		s.phaseWhen[phase] = barrier
	}

	return barrier.Wait()
}

// OnBeforeShutdown returns the before shutdown event
func (s *LifecycleMainService) OnBeforeShutdown() basecommon.Event[*BeforeShutdownEvent] {
	return s.onBeforeShutdown.Event()
}

// OnWillShutdown returns the will shutdown event
func (s *LifecycleMainService) OnWillShutdown() basecommon.Event[*ShutdownEvent] {
	return s.onWillShutdown.Event()
}

// OnWillLoadWindow returns the will load window event
func (s *LifecycleMainService) OnWillLoadWindow() basecommon.Event[*WindowLoadEvent] {
	return s.onWillLoadWindow.Event()
}

// OnBeforeCloseWindow returns the before close window event
func (s *LifecycleMainService) OnBeforeCloseWindow() basecommon.Event[windowelectronmain.ICodeWindow] {
	return s.onBeforeCloseWindow.Event()
}

// Reload reloads a window. All lifecycle event handlers are triggered.
func (s *LifecycleMainService) Reload(window windowelectronmain.ICodeWindow, cli interface{}) error {
	// Unload
	veto, err := s.Unload(window, windowelectronmain.UnloadReasonReload)
	if err != nil {
		return err // Unload failed
	}
	if veto {
		return nil // Unload was vetoed
	}

	// Reload
	window.Reload(cli)
	return nil
}

// Unload unloads a window for the provided reason. All lifecycle event handlers are triggered.
func (s *LifecycleMainService) Unload(window windowelectronmain.ICodeWindow, reason windowelectronmain.UnloadReason) (bool, error) {
	s.mu.Lock()

	// Ensure there is only one unload running at the same time
	if pending, ok := s.mapWindowIdToPendingUnload[window.ID()]; ok {
		s.mu.Unlock()
		veto := <-pending
		return veto, nil
	}

	// Start unload
	pendingUnload := make(chan bool, 1)
	s.mapWindowIdToPendingUnload[window.ID()] = pendingUnload
	s.mu.Unlock()

	// Fire `onBeforeUnload` event and veto if needed
	veto := s.fireBeforeUnload(window, reason)
	if veto {
		// Veto, so cleanup and return
		close(pendingUnload)
		delete(s.mapWindowIdToPendingUnload, window.ID())
		return true, nil
	}

	// Fire `onWillUnload` event
	s.fireWillUnload(window, reason)

	// Cleanup and return
	close(pendingUnload)
	delete(s.mapWindowIdToPendingUnload, window.ID())

	return false, nil
}

// Relaunch restarts the application with optional arguments
func (s *LifecycleMainService) Relaunch(options *IRelaunchOptions) {
	s.tracef("lifecycle (main): relaunching...")

	if s.relaunchHandler != nil && s.relaunchHandler.HandleRelaunch(options) {
		return // relaunch was handled
	}

	// Default relaunch: quit and then relaunch
	s.Quit(true) // willRestart = true
}

// Kill kills the application with exit code
func (s *LifecycleMainService) Kill(code int) {
	s.tracef("lifecycle (main): killing...")

	os.Exit(code)
}

// Quit quits the application
func (s *LifecycleMainService) Quit(willRestart bool) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.tracef("lifecycle (main): quit %v", willRestart)

	if s.pendingQuitPromise != nil {
		return nil // Already quitting
	}

	// Remember if we are about to restart
	if willRestart {
		s.stateService.SetItem(quitAndRestartKey, true)
	}

	// Go through a sequence of phases to safely quit
	go s.doQuit()

	return nil
}

func (s *LifecycleMainService) doQuit() {
	s.mu.Lock()
	s.quitRequested = true
	s.mu.Unlock()

	// Fire `onBeforeShutdown` event and check for veto
	beforeShutdownEvent := &BeforeShutdownEvent{
		Reason: ShutdownReasonQuit,
	}
	s.onBeforeShutdown.Fire(beforeShutdownEvent)

	if beforeShutdownEvent.IsVetoed() {
		s.tracef("lifecycle (main): quit was vetoed")
		s.mu.Lock()
		s.stateService.RemoveItem(quitAndRestartKey) // Vetos in `onBeforeShutdown` clear any restart flag
		s.pendingQuitPromise = nil
		s.mu.Unlock()
		return
	}

	// Unload all windows
	veto, err := s.unloadAllWindows()
	if err != nil {
		s.logService.Error("Error during window unload", "err", err)
		// We don't veto, but we log the error
	}

	if veto {
		s.tracef("lifecycle (main): quit was vetoed by window unload")
		s.mu.Lock()
		s.stateService.RemoveItem(quitAndRestartKey) // Vetos in `onBeforeShutdown` clear any restart flag
		s.pendingQuitPromise = nil
		s.mu.Unlock()
		return
	}

	// Fire `onWillShutdown` event
	s.fireWillShutdown(ShutdownReasonQuit)

	// Finally, quit
	s.Kill(0)
}

func (s *LifecycleMainService) unloadAllWindows() (bool, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.windowCounter == 0 {
		return false, nil // no windows to unload
	}

	var ( 
		veto      bool
		firstError error
		wg        sync.WaitGroup
	)

	for _, window := range s.windows {
		wg.Add(1)
		go func(window windowelectronmain.ICodeWindow) {
			defer wg.Done()
			windowVeto, err := s.Unload(window, windowelectronmain.UnloadReasonQuit)
			if windowVeto {
				veto = true
			}
			if err != nil && firstError == nil {
				firstError = err
			}
		}(window)
	}

	wg.Wait()

	return veto, firstError
}

func (s *LifecycleMainService) fireWillShutdown(reason ShutdownReason) {
	joiners := &sync.WaitGroup{}
	join := func(id string, promise <-chan error) {
		joiners.Add(1)
		go func() {
			defer joiners.Done()
			<-promise
		}()
	}

	event := &ShutdownEvent{
		Reason: reason,
		Join:   join,
	}
	s.onWillShutdown.Fire(event)

	// Wait for all joiners to complete
	done := make(chan struct{})
	go func() {
		joiners.Wait()
		close(done)
	}()

	select {
	case <-done:
		// All good
	case <-time.After(5 * time.Second):
		s.logService.Warn("onWillShutdown event joiners timed out after 5 seconds")
	}
}

func (s *LifecycleMainService) fireBeforeUnload(window windowelectronmain.ICodeWindow, reason windowelectronmain.UnloadReason) bool {
	// TODO: Implement IPC communication with renderer process
	// For now, we assume no veto.
	return false
}

func (s *LifecycleMainService) fireWillUnload(window windowelectronmain.ICodeWindow, reason windowelectronmain.UnloadReason) {
	// TODO: Implement event firing
}

// RegisterWindow makes a ICodeWindow known to the lifecycle main service
func (s *LifecycleMainService) RegisterWindow(window windowelectronmain.ICodeWindow) {
	s.mu.Lock()
	defer s.mu.Unlock()

	// track window count
	s.windowCounter++
	s.windows[window.ID()] = window

	// Window Events
	window.OnDidClose().Subscribe(func(_ struct{}) { s.onWindowClosed(window) })
}

func (s *LifecycleMainService) onWindowClosed(window windowelectronmain.ICodeWindow) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.windowCounter--
	delete(s.windows, window.ID())

	// A promise that completes when the last window is closed
	if s.windowCounter == 0 && s.pendingWhenAllWindowsClosedPromise != nil {
		close(s.pendingWhenAllWindowsClosedPromise)
		s.pendingWhenAllWindowsClosedPromise = nil
	}
}

func (s *LifecycleMainService) tracef(format string, args ...interface{}) {
	if s.environmentMainService.Args().EnableSmokeTestDriver {
		s.logService.Info(format, args...)
	} else {
		s.logService.Trace(format, args...)
	}
}

// Service identifier
var ILifecycleMainServiceID = instantiationcommon.CreateDecorator[ILifecycleMainService]("lifecycleMainService")


// ShutdownReason represents the reason for application shutdown
type ShutdownReason int

const (
	// ShutdownReasonQuit - The application exits normally
	ShutdownReasonQuit ShutdownReason = 1
	// ShutdownReasonKill - The application exits abnormally and is being killed with an exit code
	ShutdownReasonKill ShutdownReason = 2
)

// BeforeShutdownEvent represents a before-shutdown event
type BeforeShutdownEvent struct {
	Reason ShutdownReason
	veto   bool
	mu     sync.Mutex
}

// Veto signals that the shutdown should be vetoed
func (e *BeforeShutdownEvent) Veto(veto bool) {
	e.mu.Lock()
	defer e.mu.Unlock()
	e.veto = e.veto || veto
}

// IsVetoed returns whether the shutdown is vetoed
func (e *BeforeShutdownEvent) IsVetoed() bool {
	e.mu.Lock()
	defer e.mu.Unlock()
	return e.veto
}

// ShutdownEvent represents a shutdown event
type ShutdownEvent struct {
	// Reason - More details why the application is shutting down
	Reason ShutdownReason
	// Join allows to join the shutdown. The promise can be a long running operation but it will block the application from closing
	Join func(id string, promise <-chan error)
}

// WindowLoadEvent represents a window load event
type WindowLoadEvent struct {
	// Window - The window that is loaded to a new workspace
	Window windowelectronmain.ICodeWindow
	// Workspace - The workspace the window is loaded into
	Workspace workspacecommon.IAnyWorkspaceIdentifier
	// Reason - More details why the window loads to a new workspace
	Reason windowelectronmain.LoadReason
}

// LifecycleMainPhase represents the lifecycle phase
type LifecycleMainPhase int

const (
	// LifecycleMainPhaseStarting - Starting phase
	LifecycleMainPhaseStarting LifecycleMainPhase = 1
	// LifecycleMainPhaseReady - Ready phase
	LifecycleMainPhaseReady LifecycleMainPhase = 2
	// LifecycleMainPhaseAfterWindowOpen - After window open phase
	LifecycleMainPhaseAfterWindowOpen LifecycleMainPhase = 3
	// LifecycleMainPhaseEventuallyReady - Eventually ready phase
	LifecycleMainPhaseEventuallyReady LifecycleMainPhase = 4
)

// IRelaunchOptions represents relaunch options
type IRelaunchOptions struct {
	AddArgs    []string
	RemoveArgs []string
}

// IRelaunchHandler allows a handler to deal with relaunching the application
type IRelaunchHandler interface {
	// HandleRelaunch allows a handler to deal with relaunching the application.
	// The return value indicates if the relaunch is handled or not.
	HandleRelaunch(options *IRelaunchOptions) bool
}

// ILifecycleMainService represents the main lifecycle service interface
type ILifecycleMainService interface {
	// WasRestarted - Will be true if the program was restarted (e.g. due to explicit request or update)
	WasRestarted() bool

	// QuitRequested - Will be true if the program was requested to quit
	QuitRequested() bool

	// Phase - A flag indicating in what phase of the lifecycle we currently are
	Phase() LifecycleMainPhase
	SetPhase(phase LifecycleMainPhase)

	// OnBeforeShutdown - An event that fires when the application is about to shutdown before any window is closed.
	// The shutdown can still be prevented by any window that vetos this event.
	OnBeforeShutdown() basecommon.Event[*BeforeShutdownEvent]

	// OnWillShutdown - An event that fires after the onBeforeShutdown event has been fired and after no window has
	// vetoed the shutdown sequence. At this point listeners are ensured that the application will quit without veto.
	OnWillShutdown() basecommon.Event[*ShutdownEvent]

	// OnWillLoadWindow - An event that fires when a window is loading. This can either be a window opening for the
	// first time or a window reloading or changing to another URL.
	OnWillLoadWindow() basecommon.Event[*WindowLoadEvent]

	// OnBeforeCloseWindow - An event that fires before a window closes. This event is fired after any veto has been dealt
	// with so that listeners know for sure that the window will close without veto.
	OnBeforeCloseWindow() basecommon.Event[windowelectronmain.ICodeWindow]

	// RegisterWindow - Make a ICodeWindow known to the lifecycle main service
	RegisterWindow(window windowelectronmain.ICodeWindow)

	// Reload - Reload a window. All lifecycle event handlers are triggered
	Reload(window windowelectronmain.ICodeWindow, cli interface{}) error

	// Unload - Unload a window for the provided reason. All lifecycle event handlers are triggered
	Unload(window windowelectronmain.ICodeWindow, reason windowelectronmain.UnloadReason) (bool, error)

	// Relaunch - Restart the application with optional arguments
	Relaunch(options *IRelaunchOptions)

	// Quit - Quit the application
	Quit(willRestart bool) error

	// Kill - Kill the application with exit code
	Kill(code int)
}
