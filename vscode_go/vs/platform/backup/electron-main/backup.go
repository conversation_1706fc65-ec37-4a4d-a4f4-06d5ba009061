/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package electronmain

import (
	backupcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/backup/common"
	backupnode "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/backup/node"
	instantiationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
)

var IBackupMainServiceID = instantiationcommon.CreateDecorator[IBackupMainService]("backupMainService")

// IBackupMainService is the main backup service interface.
type IBackupMainService interface {
	IsHotExitEnabled() bool
	GetEmptyWindowBackups() []backupnode.IEmptyWindowBackupInfo
	RegisterWorkspaceBackup(workspaceInfo backupcommon.IWorkspaceBackupInfo, migrateFrom ...string) (string, error)
	RegisterFolderBackup(folderInfo backupcommon.IFolderBackupInfo) string
	RegisterEmptyWindowBackup(emptyWindowInfo backupnode.IEmptyWindowBackupInfo) string
	GetDirtyWorkspaces() ([]interface{}, error) // []IWorkspaceBackupInfo | IFolderBackupInfo
}
