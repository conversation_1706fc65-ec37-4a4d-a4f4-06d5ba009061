package common

// INativeCliOptions defines native CLI options.
// Note: In Go, boolean fields are typically represented without pointers if `false` is the desired zero value.
// Using pointers for optional booleans is an option if `nil` needs to be distinct from `false`.
// For simplicity, we'll use bool and assume `false` is the default.
type INativeCliOptions struct {
	CliDataDir       string `json:"cli-data-dir,omitempty"`
	DisableTelemetry bool   `json:"disable-telemetry,omitempty"`
	TelemetryLevel   string `json:"telemetry-level,omitempty"`
}

// NativeParsedArgs represents the supported command-line arguments.
// We use struct tags to map the JSON keys to the Go struct fields.
type NativeParsedArgs struct {
	// Subcommands
	Tunnel *struct {
		INativeCliOptions
		User struct {
			Login struct {
				AccessToken string `json:"access-token,omitempty"`
				Provider    string `json:"provider,omitempty"`
			} `json:"login,omitempty"`
		} `json:"user,omitempty"`
	} `json:"tunnel,omitempty"`
	ServeWeb *INativeCliOptions `json:"serve-web,omitempty"`
	Chat     *struct {
		Args        []string `json:"_"`
		AddFile     []string `json:"add-file,omitempty"`
		Mode        string   `json:"mode,omitempty"`
		Maximize    bool     `json:"maximize,omitempty"`
		ReuseWindow bool     `json:"reuse-window,omitempty"`
		NewWindow   bool     `json:"new-window,omitempty"`
		Help        bool     `json:"help,omitempty"`
	} `json:"chat,omitempty"`

	// Arguments
	Args                          []string `json:"_"`
	FolderURI                     []string `json:"folder-uri,omitempty"`
	FileURI                       []string `json:"file-uri,omitempty"`
	URLs                          []string `json:"_urls,omitempty"`
	Help                          bool     `json:"help,omitempty"`
	Version                       bool     `json:"version,omitempty"`
	Telemetry                     bool     `json:"telemetry,omitempty"`
	Status                        bool     `json:"status,omitempty"`
	Wait                          bool     `json:"wait,omitempty"`
	WaitMarkerFilePath            string   `json:"waitMarkerFilePath,omitempty"`
	Diff                          bool     `json:"diff,omitempty"`
	Merge                         bool     `json:"merge,omitempty"`
	Add                           bool     `json:"add,omitempty"`
	Remove                        bool     `json:"remove,omitempty"`
	Goto                          bool     `json:"goto,omitempty"`
	NewWindow                     bool     `json:"new-window,omitempty"`
	ReuseWindow                   bool     `json:"reuse-window,omitempty"`
	Locale                        string   `json:"locale,omitempty"`
	UserDataDir                   string   `json:"user-data-dir,omitempty"`
	ProfStartup                   bool     `json:"prof-startup,omitempty"`
	ProfStartupPrefix             string   `json:"prof-startup-prefix,omitempty"`
	ProfAppendTimers              string   `json:"prof-append-timers,omitempty"`
	ProfDurationMarkers           []string `json:"prof-duration-markers,omitempty"`
	ProfDurationMarkersFile       string   `json:"prof-duration-markers-file,omitempty"`
	ProfV8Extensions              bool     `json:"prof-v8-extensions,omitempty"`
	NoCachedData                  bool     `json:"no-cached-data,omitempty"`
	Verbose                       bool     `json:"verbose,omitempty"`
	Trace                         bool     `json:"trace,omitempty"`
	TraceMemoryInfra              bool     `json:"trace-memory-infra,omitempty"`
	TraceCategoryFilter           string   `json:"trace-category-filter,omitempty"`
	TraceOptions                  string   `json:"trace-options,omitempty"`
	OpenDevTools                  bool     `json:"open-devtools,omitempty"`
	Log                           []string `json:"log,omitempty"`
	LogExtensionHostCommunication bool     `json:"logExtensionHostCommunication,omitempty"`
	ExtensionsDir                 string   `json:"extensions-dir,omitempty"`
	ExtensionsDownloadDir         string   `json:"extensions-download-dir,omitempty"`
	BuiltinExtensionsPath         string   `json:"builtin-extensions-dir,omitempty"`
	ExtensionDevelopmentPath      []string `json:"extensionDevelopmentPath,omitempty"`
	ExtensionTestsPath            string   `json:"extensionTestsPath,omitempty"`
	ExtensionDevelopmentKind      []string `json:"extensionDevelopmentKind,omitempty"`
	ExtensionEnvironment          string   `json:"extensionEnvironment,omitempty"`
	InspectExtensions             string   `json:"inspect-extensions,omitempty"`
	InspectBrkExtensions          string   `json:"inspect-brk-extensions,omitempty"`
	DebugID                       string   `json:"debugId,omitempty"`
	DebugRenderer                 bool     `json:"debugRenderer,omitempty"`
	InspectSearch                 string   `json:"inspect-search,omitempty"`
	InspectBrkSearch              string   `json:"inspect-brk-search,omitempty"`
	InspectPtyHost                string   `json:"inspect-ptyhost,omitempty"`
	InspectBrkPtyHost             string   `json:"inspect-brk-ptyhost,omitempty"`
	InspectSharedProcess          string   `json:"inspect-sharedprocess,omitempty"`
	InspectBrkSharedProcess       string   `json:"inspect-brk-sharedprocess,omitempty"`
	DisableExtensions             bool     `json:"disable-extensions,omitempty"`
	DisableExtension              []string `json:"disable-extension,omitempty"`
	ListExtensions                bool     `json:"list-extensions,omitempty"`
	ShowVersions                  bool     `json:"show-versions,omitempty"`
	Category                      string   `json:"category,omitempty"`
	InstallExtension              []string `json:"install-extension,omitempty"`
	PreRelease                    bool     `json:"pre-release,omitempty"`
	InstallBuiltinExtension       []string `json:"install-builtin-extension,omitempty"`
	UninstallExtension            []string `json:"uninstall-extension,omitempty"`
	UpdateExtensions              bool     `json:"update-extensions,omitempty"`
	DoNotIncludePackDependencies  bool     `json:"do-not-include-pack-dependencies,omitempty"`
	LocateExtension               []string `json:"locate-extension,omitempty"`
	EnableProposedAPI             []string `json:"enable-proposed-api,omitempty"`
	OpenURL                       bool     `json:"open-url,omitempty"`
	SkipReleaseNotes              bool     `json:"skip-release-notes,omitempty"`
	SkipWelcome                   bool     `json:"skip-welcome,omitempty"`
	DisableTelemetry              bool     `json:"disable-telemetry,omitempty"`
	ExportDefaultConfiguration    string   `json:"export-default-configuration,omitempty"`
	InstallSource                 string   `json:"install-source,omitempty"`
	AddMCP                        []string `json:"add-mcp,omitempty"`
	DisableUpdates                bool     `json:"disable-updates,omitempty"`
	UseInMemorySecretStorage      bool     `json:"use-inmemory-secretstorage,omitempty"`
	PasswordStore                 string   `json:"password-store,omitempty"`
	DisableWorkspaceTrust         bool     `json:"disable-workspace-trust,omitempty"`
	DisableCrashReporter          bool     `json:"disable-crash-reporter,omitempty"`
	CrashReporterDirectory        string   `json:"crash-reporter-directory,omitempty"`
	CrashReporterID               string   `json:"crash-reporter-id,omitempty"`
	SkipAddToRecentlyOpened       bool     `json:"skip-add-to-recently-opened,omitempty"`
	FileWrite                     bool     `json:"file-write,omitempty"`
	FileChmod                     bool     `json:"file-chmod,omitempty"`
	EnableSmokeTestDriver         bool     `json:"enable-smoke-test-driver,omitempty"`
	Remote                        string   `json:"remote,omitempty"`
	Force                         bool     `json:"force,omitempty"`
	DoNotSync                     bool     `json:"do-not-sync,omitempty"`
	PreserveEnv                   bool     `json:"preserve-env,omitempty"`
	ForceUserEnv                  bool     `json:"force-user-env,omitempty"`
	ForceDisableUserEnv           bool     `json:"force-disable-user-env,omitempty"`
	Sync                          string   `json:"sync,omitempty"` // 'on' or 'off'
	LogsPath                      string   `json:"logsPath,omitempty"`
	EnableFilePolicy              bool     `json:"__enable-file-policy,omitempty"`
	EditSessionID                 string   `json:"editSessionId,omitempty"`
	ContinueOn                    string   `json:"continueOn,omitempty"`
	LocateShellIntegrationPath    string   `json:"locate-shell-integration-path,omitempty"`
	Profile                       string   `json:"profile,omitempty"`
	ProfileTemp                   bool     `json:"profile-temp,omitempty"`
	DisableChromiumSandbox        bool     `json:"disable-chromium-sandbox,omitempty"`
	Sandbox                       bool     `json:"sandbox,omitempty"`
	EnableCOI                     bool     `json:"enable-coi,omitempty"`
	UnresponsiveSampleInterval    string   `json:"unresponsive-sample-interval,omitempty"`
	UnresponsiveSamplePeriod      string   `json:"unresponsive-sample-period,omitempty"`
	EnableRDPDisplayTracking      bool     `json:"enable-rdp-display-tracking,omitempty"`
	DisableLayoutRestore          bool     `json:"disable-layout-restore,omitempty"`
	StartupExperimentGroup        string   `json:"startup-experiment-group,omitempty"`
	DisableExperiments            bool     `json:"disable-experiments,omitempty"`

	// Chromium command line args
	NoProxyServer              bool   `json:"no-proxy-server,omitempty"`
	NoSandbox                  bool   `json:"no-sandbox,omitempty"`
	ProxyServer                string `json:"proxy-server,omitempty"`
	ProxyBypassList            string `json:"proxy-bypass-list,omitempty"`
	ProxyPacURL                string `json:"proxy-pac-url,omitempty"`
	Inspect                    string `json:"inspect,omitempty"`
	InspectBrk                 string `json:"inspect-brk,omitempty"`
	JSFlags                    string `json:"js-flags,omitempty"`
	DisableLCDText             bool   `json:"disable-lcd-text,omitempty"`
	DisableGPU                 bool   `json:"disable-gpu,omitempty"`
	DisableGPUSandbox          bool   `json:"disable-gpu-sandbox,omitempty"`
	NoLazy                     bool   `json:"nolazy,omitempty"`
	ForceDeviceScaleFactor     string `json:"force-device-scale-factor,omitempty"`
	ForceRendererAccessibility bool   `json:"force-renderer-accessibility,omitempty"`
	IgnoreCertificateErrors    bool   `json:"ignore-certificate-errors,omitempty"`
	AllowInsecureLocalhost     bool   `json:"allow-insecure-localhost,omitempty"`
	LogNetLog                  string `json:"log-net-log,omitempty"`
	VModule                    string `json:"vmodule,omitempty"`
	DisableDevShmUsage         bool   `json:"disable-dev-shm-usage,omitempty"`
	OzonePlatform              string `json:"ozone-platform,omitempty"`
	EnableTracing              string `json:"enable-tracing,omitempty"`
	TraceStartupFormat         string `json:"trace-startup-format,omitempty"`
	TraceStartupFile           string `json:"trace-startup-file,omitempty"`
	TraceStartupDuration       string `json:"trace-startup-duration,omitempty"`
	XDGPortalRequiredVersion   string `json:"xdg-portal-required-version,omitempty"`
}
