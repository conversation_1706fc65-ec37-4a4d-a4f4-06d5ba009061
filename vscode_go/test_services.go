package main

import (
	"fmt"

	environmentcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment/common"
	environmentelectronmain "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment/electron-main"
	logelectronmain "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/electron-main"
)

func main() {
	fmt.Println("Testing migrated Go services...")

	// Test Environment Main Service
	fmt.Println("\n=== Testing EnvironmentMainService ===")
	
	// Create mock args
	args := environmentcommon.NativeParsedArgs{
		"disable-updates": true,
		"enable-coi":      false,
	}

	// Create environment service
	envService := environmentelectronmain.NewEnvironmentMainService(args, nil)
	
	fmt.Printf("User Data Path: %s\n", envService.UserDataPath())
	fmt.Printf("Backup Home: %s\n", envService.BackupHome())
	fmt.Printf("Main IPC Handle: %s\n", envService.MainIPCHandle())
	fmt.Printf("Main Lockfile: %s\n", envService.MainLockfile())
	fmt.Printf("Disable Updates: %t\n", envService.DisableUpdates())
	fmt.Printf("Cross Origin Isolated: %t\n", envService.CrossOriginIsolated())
	fmt.Printf("Enable RDP Display Tracking: %t\n", envService.EnableRDPDisplayTracking())
	fmt.Printf("Use Code Cache: %t\n", envService.UseCodeCache())

	// Test snap environment variables (safe to call on any platform)
	envService.UnsetSnapExportedVariables()
	envService.RestoreSnapExportedVariables()
	fmt.Println("Snap environment variable handling: OK")

	// Test Logger Main Service
	fmt.Println("\n=== Testing LoggerMainService ===")
	
	loggerService := logelectronmain.NewLoggerMainService()
	
	fmt.Printf("Logger service created: %T\n", loggerService)
	fmt.Printf("Service brand: %v\n", loggerService.ServiceBrand())
	
	// Test global loggers
	globalLoggers := loggerService.GetGlobalLoggers()
	fmt.Printf("Global loggers count: %d\n", len(globalLoggers))
	
	// Test window-specific event handlers
	windowID := 1
	logLevelEvent := loggerService.GetOnDidChangeLogLevelEvent(windowID)
	visibilityEvent := loggerService.GetOnDidChangeVisibilityEvent(windowID)
	loggersEvent := loggerService.GetOnDidChangeLoggersEvent(windowID)
	
	fmt.Printf("Log level event created: %T\n", logLevelEvent)
	fmt.Printf("Visibility event created: %T\n", visibilityEvent)
	fmt.Printf("Loggers event created: %T\n", loggersEvent)

	// Test deregistering loggers for a window
	loggerService.DeregisterLoggers(windowID)
	fmt.Println("Deregistered loggers for window:", windowID)

	fmt.Println("\n=== All tests completed successfully! ===")
}